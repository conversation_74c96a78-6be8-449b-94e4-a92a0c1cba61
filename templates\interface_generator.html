{% extends "admin_base.html" %}

{% block title %}Interface Generator - ERDB Knowledge Hub{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-code me-2"></i>
                        Custom Interface Generator
                    </h4>
                    <p class="text-muted mb-0">Generate customized chat interfaces with pre-configured settings</p>
                </div>
                <div class="card-body">
                    <!-- Interface Generation Form -->
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Generate New Interface</h5>
                                </div>
                                <div class="card-body">
                                    <form id="interfaceGeneratorForm">
                                        <!-- Interface Name -->
                                        <div class="mb-3">
                                            <label for="interfaceName" class="form-label">Interface Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="interfaceName" name="interface_name" 
                                                   placeholder="e.g., CANOPY Research Interface" required maxlength="100">
                                            <div class="form-text">This will be displayed as the page title</div>
                                        </div>

                                        <!-- Custom Title -->
                                        <div class="mb-3">
                                            <label for="customTitle" class="form-label">Custom Page Title</label>
                                            <input type="text" class="form-control" id="customTitle" name="custom_title" 
                                                   placeholder="Leave empty to auto-generate">
                                            <div class="form-text">Optional: Custom HTML page title</div>
                                        </div>

                                        <!-- Anti-Hallucination Mode -->
                                        <div class="mb-3">
                                            <label class="form-label">Anti-Hallucination Mode</label>
                                            <div id="antiHallucinationModes">
                                                <!-- Will be populated by JavaScript -->
                                            </div>
                                            <div class="form-text">Pre-configure the response mode (leave unselected for user choice)</div>
                                        </div>

                                        <!-- AI Model Selection -->
                                        <div class="mb-3">
                                            <label for="selectedModel" class="form-label">AI Model</label>
                                            <select class="form-select" id="selectedModel" name="selected_model">
                                                <option value="">Allow user to select model</option>
                                                <!-- Will be populated by JavaScript -->
                                            </select>
                                            <div class="form-text">Pre-select an AI model (leave empty for user choice)</div>
                                        </div>

                                        <!-- Categories -->
                                        <div class="mb-3">
                                            <label class="form-label">Document Categories</label>
                                            <div id="categoriesCheckboxes">
                                                <!-- Will be populated by JavaScript -->
                                            </div>
                                            <div class="form-text">Select categories to limit the interface to (leave empty for all categories)</div>
                                        </div>

                                        <!-- Generate Button -->
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-primary" id="generateBtn">
                                                <i class="fas fa-cog me-2"></i>
                                                Generate Interface
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Generated Interfaces List -->
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">Generated Interfaces</h5>
                                    <button class="btn btn-sm btn-outline-primary" onclick="loadGeneratedInterfaces()">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        Refresh
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="generatedInterfacesList">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin me-2"></i>
                                            Loading interfaces...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Modals -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resultModalTitle">Result</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resultModalBody">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="viewInterfaceBtn" style="display: none;">
                    View Interface
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let configOptions = {};
let generatedInterfaces = [];

// Load configuration options on page load
document.addEventListener('DOMContentLoaded', function() {
    loadConfigOptions();
    loadGeneratedInterfaces();
});

async function loadConfigOptions() {
    try {
        const result = await DMSUtils.api('/api/interface-config-options');

        if (result.success) {
            configOptions = result.options;
            populateConfigOptions();
        } else {
            showError('Failed to load configuration options: ' + result.error);
        }
    } catch (error) {
        console.error('Error loading config options:', error);
        showError('Failed to load configuration options');
    }
}

function populateConfigOptions() {
    // Populate anti-hallucination modes
    const modesContainer = document.getElementById('antiHallucinationModes');
    const modes = configOptions.anti_hallucination_modes;
    
    if (modes && modes.available_modes) {
        modesContainer.innerHTML = '';
        modes.available_modes.forEach(mode => {
            const description = modes.mode_descriptions ? modes.mode_descriptions[mode] : mode;
            const div = document.createElement('div');
            div.className = 'form-check';
            div.innerHTML = `
                <input class="form-check-input" type="radio" name="anti_hallucination_mode" 
                       id="mode_${mode}" value="${mode}">
                <label class="form-check-label" for="mode_${mode}">
                    <strong>${mode.charAt(0).toUpperCase() + mode.slice(1)}</strong>
                    <br><small class="text-muted">${description}</small>
                </label>
            `;
            modesContainer.appendChild(div);
        });
    }
    
    // Populate models
    const modelSelect = document.getElementById('selectedModel');
    if (configOptions.models) {
        configOptions.models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.name;
            option.textContent = model.display_name;
            modelSelect.appendChild(option);
        });
    }
    
    // Populate categories
    const categoriesContainer = document.getElementById('categoriesCheckboxes');
    if (configOptions.categories) {
        categoriesContainer.innerHTML = '';
        configOptions.categories.forEach(category => {
            const div = document.createElement('div');
            div.className = 'form-check';
            div.innerHTML = `
                <input class="form-check-input" type="checkbox" name="categories" 
                       id="cat_${category}" value="${category}">
                <label class="form-check-label" for="cat_${category}">
                    ${category}
                </label>
            `;
            categoriesContainer.appendChild(div);
        });
    }
}

async function loadGeneratedInterfaces() {
    try {
        const result = await DMSUtils.api('/api/generated-interfaces');

        if (result.success) {
            generatedInterfaces = result.interfaces;
            displayGeneratedInterfaces();
        } else {
            showError('Failed to load generated interfaces: ' + result.error);
        }
    } catch (error) {
        console.error('Error loading generated interfaces:', error);
        showError('Failed to load generated interfaces');
    }
}

function displayGeneratedInterfaces() {
    const container = document.getElementById('generatedInterfacesList');
    
    if (generatedInterfaces.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-file-code me-2"></i>
                No interfaces generated yet
            </div>
        `;
        return;
    }
    
    container.innerHTML = '';
    
    generatedInterfaces.forEach(interface => {
        const config = interface.config || {};
        const generatedDate = new Date(interface.generated_at).toLocaleString();
        const fileSize = formatFileSize(interface.file_size);
        
        const div = document.createElement('div');
        div.className = 'border rounded p-3 mb-3';
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${config.interface_name || 'Unnamed Interface'}</h6>
                    <small class="text-muted d-block">Generated: ${generatedDate}</small>
                    <small class="text-muted d-block">Size: ${fileSize}</small>
                    
                    ${config.anti_hallucination_mode ? `<span class="badge bg-info me-1">Mode: ${config.anti_hallucination_mode}</span>` : ''}
                    ${config.selected_model ? `<span class="badge bg-success me-1">Model: ${config.selected_model}</span>` : ''}
                    ${config.categories && config.categories.length > 0 ? `<span class="badge bg-warning me-1">Categories: ${config.categories.join(', ')}</span>` : ''}
                </div>
                <div class="btn-group-vertical btn-group-sm">
                    <a href="/generated/${interface.filename}" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>
                        View
                    </a>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteInterface('${interface.filename}')">
                        <i class="fas fa-trash me-1"></i>
                        Delete
                    </button>
                </div>
            </div>
        `;
        container.appendChild(div);
    });
}

// Handle form submission
document.getElementById('interfaceGeneratorForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const generateBtn = document.getElementById('generateBtn');
    const originalText = generateBtn.innerHTML;
    
    try {
        // Show loading state
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
        generateBtn.disabled = true;
        
        // Collect form data
        const formData = new FormData(this);
        const config = {
            interface_name: formData.get('interface_name'),
            custom_title: formData.get('custom_title'),
            anti_hallucination_mode: formData.get('anti_hallucination_mode'),
            selected_model: formData.get('selected_model'),
            categories: formData.getAll('categories')
        };
        
        // Remove empty values
        Object.keys(config).forEach(key => {
            if (!config[key] || (Array.isArray(config[key]) && config[key].length === 0)) {
                delete config[key];
            }
        });
        
        // Send request using the utilities API function for proper CSRF handling
        const result = await DMSUtils.api('/api/generate-interface', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config)
        });
        
        if (result.success) {
            showSuccess(result.message, result.filename);
            this.reset(); // Clear form
            loadGeneratedInterfaces(); // Refresh list
        } else {
            showError(result.error);
        }
        
    } catch (error) {
        console.error('Error generating interface:', error);
        showError('Failed to generate interface');
    } finally {
        // Restore button state
        generateBtn.innerHTML = originalText;
        generateBtn.disabled = false;
    }
});

async function deleteInterface(filename) {
    if (!confirm(`Are you sure you want to delete the interface "${filename}"?`)) {
        return;
    }

    try {
        const result = await DMSUtils.api(`/api/generated-interfaces/${filename}`, {
            method: 'DELETE'
        });

        if (result.success) {
            showSuccess(result.message);
            loadGeneratedInterfaces(); // Refresh list
        } else {
            showError(result.error);
        }

    } catch (error) {
        console.error('Error deleting interface:', error);
        showError('Failed to delete interface');
    }
}

function showSuccess(message, filename = null) {
    const modal = new bootstrap.Modal(document.getElementById('resultModal'));
    document.getElementById('resultModalTitle').textContent = 'Success';
    document.getElementById('resultModalBody').innerHTML = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
        </div>
    `;
    
    const viewBtn = document.getElementById('viewInterfaceBtn');
    if (filename) {
        viewBtn.style.display = 'inline-block';
        viewBtn.onclick = () => window.open(`/generated/${filename}`, '_blank');
    } else {
        viewBtn.style.display = 'none';
    }
    
    modal.show();
}

function showError(message) {
    const modal = new bootstrap.Modal(document.getElementById('resultModal'));
    document.getElementById('resultModalTitle').textContent = 'Error';
    document.getElementById('resultModalBody').innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
    
    document.getElementById('viewInterfaceBtn').style.display = 'none';
    modal.show();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>
{% endblock %}
