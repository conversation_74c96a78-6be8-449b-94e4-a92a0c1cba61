"""
HTML Generator Service for Custom Chat Interfaces

This module provides functionality to generate customized HTML chat interfaces
based on the existing index.html template with pre-configured settings.
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import re
from jinja2 import Template

logger = logging.getLogger(__name__)

class HTMLGenerator:
    """Service for generating customized HTML chat interfaces."""
    
    def __init__(self, template_path: str = "templates/index.html", output_dir: str = "frontend"):
        """
        Initialize the HTML generator.
        
        Args:
            template_path: Path to the base template file
            output_dir: Directory to save generated HTML files
        """
        self.template_path = template_path
        self.output_dir = output_dir
        self.ensure_output_directory()
        
    def ensure_output_directory(self):
        """Ensure the output directory exists."""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"Created output directory: {self.output_dir}")
    
    def load_template(self) -> str:
        """Load the base HTML template."""
        try:
            with open(self.template_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error loading template from {self.template_path}: {str(e)}")
            raise
    
    def get_available_categories(self) -> List[str]:
        """Get list of available document categories."""
        chroma_path = os.getenv("CHROMA_PATH", "./chroma")
        if os.path.exists(chroma_path):
            return sorted(os.listdir(chroma_path))
        return []
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available AI models."""
        try:
            # Import here to avoid circular imports
            from app import get_models_data
            models_data = get_models_data()
            return models_data.get('models', [])
        except Exception as e:
            logger.error(f"Error getting available models: {str(e)}")
            # Return fallback models
            return [{
                'name': 'llama3.2:3b-instruct-q4_K_M',
                'display_name': 'Llama 3.2 3B',
                'description': 'Fast, efficient model for general queries',
                'capability': 'General Purpose',
                'size_formatted': '2.0 GB'
            }]
    
    def get_anti_hallucination_modes(self) -> Dict[str, Any]:
        """Get available anti-hallucination modes from configuration."""
        try:
            default_models_file = "default_models.json"
            if os.path.exists(default_models_file):
                with open(default_models_file, 'r') as f:
                    config = json.load(f)
                    return config.get('query_parameters', {}).get('anti_hallucination_modes', {})
        except Exception as e:
            logger.error(f"Error loading anti-hallucination modes: {str(e)}")
        
        # Return default modes
        return {
            'default_mode': 'strict',
            'available_modes': ['strict', 'balanced', 'off'],
            'mode_descriptions': {
                'strict': 'Only respond with information directly found in documents',
                'balanced': 'Allow limited inference while citing sources',
                'off': 'Allow more creative responses with external knowledge'
            }
        }
    
    def customize_html_template(self, template_content: str, config: Dict[str, Any]) -> str:
        """
        Customize the HTML template based on configuration.
        
        Args:
            template_content: Original HTML template content
            config: Configuration dictionary with customization settings
            
        Returns:
            Customized HTML content
        """
        # Extract configuration values
        anti_hallucination_mode = config.get('anti_hallucination_mode', 'strict')
        selected_model = config.get('selected_model', '')
        categories = config.get('categories', [])
        interface_name = config.get('interface_name', 'Custom Interface')
        custom_title = config.get('custom_title', f'ERDB Knowledge Hub - {interface_name}')
        
        # Replace page title
        template_content = re.sub(
            r'<title>.*?</title>',
            f'<title>{custom_title}</title>',
            template_content
        )
        
        # Update welcome title and subtitle
        template_content = re.sub(
            r'<h1 class="welcome-title">.*?</h1>',
            f'<h1 class="welcome-title">{interface_name}</h1>',
            template_content
        )
        
        # Remove category selection dropdown and replace with hidden input
        if categories:
            category_options = '\n'.join([f'<option value="{cat}">{cat}</option>' for cat in categories])
            # If only one category, pre-select it
            if len(categories) == 1:
                selected_category = categories[0]
                category_replacement = f'''
                    <input type="hidden" id="category" value="{selected_category}">
                    <div class="input-group">
                        <label class="input-label">Category: <strong>{selected_category}</strong></label>
                        <div class="alert alert-info">
                            This interface is configured for the <strong>{selected_category}</strong> category.
                        </div>
                    </div>'''
            else:
                # Multiple categories - show as read-only dropdown
                category_replacement = f'''
                    <div class="input-group">
                        <label for="category" class="input-label">Category</label>
                        <select id="category" class="input-select">
                            <option value="">- Select Category -</option>
                            {category_options}
                        </select>
                        <div class="alert alert-info">
                            This interface is limited to: {", ".join(categories)}
                        </div>
                    </div>'''
        else:
            # No categories specified - keep original behavior
            category_replacement = '''
                <div class="input-group">
                    <label for="category" class="input-label">Category</label>
                    <select id="category" class="input-select">
                        <option value="">- Select Category -</option>
                        {% for category in categories %}
                            <option value="{{ category }}">{{ category }}</option>
                        {% endfor %}
                    </select>
                </div>'''
        
        # Replace category selection section - more specific pattern
        category_pattern = r'<div class="input-group">\s*<label for="category"[^>]*>.*?</select>\s*</div>'
        template_content = re.sub(
            category_pattern,
            category_replacement,
            template_content,
            flags=re.DOTALL
        )
        
        return template_content
    
    def inject_custom_javascript(self, template_content: str, config: Dict[str, Any]) -> str:
        """
        Inject custom JavaScript for pre-configured settings.
        
        Args:
            template_content: HTML template content
            config: Configuration dictionary
            
        Returns:
            HTML content with injected JavaScript
        """
        anti_hallucination_mode = config.get('anti_hallucination_mode', 'strict')
        selected_model = config.get('selected_model', '')
        categories = config.get('categories', [])
        
        # Create custom JavaScript configuration
        custom_js = f'''
        <script>
            // Custom Interface Configuration
            window.CUSTOM_INTERFACE_CONFIG = {{
                antiHallucinationMode: '{anti_hallucination_mode}',
                selectedModel: '{selected_model}',
                categories: {json.dumps(categories)},
                isCustomInterface: true
            }};
            
            // Override model selection and anti-hallucination mode
            document.addEventListener('DOMContentLoaded', function() {{
                // Pre-configure anti-hallucination mode
                const antiHallucinationRadio = document.querySelector('input[name="anti_hallucination_mode"][value="{anti_hallucination_mode}"]');
                if (antiHallucinationRadio) {{
                    antiHallucinationRadio.checked = true;
                }}
                
                // Pre-configure selected model
                if ('{selected_model}') {{
                    selectedModel = '{selected_model}';
                    localStorage.setItem('selectedModel', selectedModel);
                }}
                
                // Category validation for custom interface
                if ({json.dumps(categories)}.length > 0) {{
                    const originalSendQuery = window.sendQuery;
                    window.sendQuery = function() {{
                        const category = document.getElementById('category').value;
                        const allowedCategories = {json.dumps(categories)};
                        
                        if (allowedCategories.length > 0 && !allowedCategories.includes(category)) {{
                            alert('This interface is limited to the following categories: ' + allowedCategories.join(', '));
                            return;
                        }}
                        
                        return originalSendQuery.apply(this, arguments);
                    }};
                }}
            }});
        </script>
        '''
        
        # Insert custom JavaScript before the closing body tag
        template_content = template_content.replace('</body>', f'{custom_js}\n</body>')
        
        return template_content
    
    def remove_user_controls(self, template_content: str, config: Dict[str, Any]) -> str:
        """
        Remove or modify user controls based on configuration.
        
        Args:
            template_content: HTML template content
            config: Configuration dictionary
            
        Returns:
            Modified HTML content
        """
        # Remove anti-hallucination mode selector if pre-configured
        if config.get('anti_hallucination_mode'):
            # Replace with hidden input and info display
            mode = config['anti_hallucination_mode']
            mode_descriptions = self.get_anti_hallucination_modes().get('mode_descriptions', {})
            mode_desc = mode_descriptions.get(mode, mode.title())
            
            replacement = f'''
                <div class="mode-selector">
                    <div class="mode-header">
                        <svg class="mode-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                        <h3 class="mode-title">Anti-Hallucination Mode: <strong>{mode.title()}</strong></h3>
                    </div>
                    <div class="alert alert-info">
                        <strong>Pre-configured:</strong> {mode_desc}
                    </div>
                    <input type="hidden" name="anti_hallucination_mode" value="{mode}">
                </div>'''
            
            template_content = re.sub(
                r'<div class="mode-selector">.*?</div>',
                replacement,
                template_content,
                flags=re.DOTALL
            )
        
        # Remove model selector if pre-configured
        if config.get('selected_model'):
            model_name = config['selected_model']
            # Find model display name
            models = self.get_available_models()
            model_display = next((m.get('display_name', model_name) for m in models if m['name'] == model_name), model_name)
            
            replacement = f'''
                <div class="input-group">
                    <label class="input-label">AI Model: <strong>{model_display}</strong></label>
                    <div class="alert alert-info">
                        This interface is pre-configured to use the <strong>{model_display}</strong> model.
                    </div>
                    <input type="hidden" id="model-selector" value="{model_name}">
                </div>'''
            
            # More specific pattern for model selector
            model_pattern = r'<div class="input-group">\s*<label for="model-selector"[^>]*>.*?</div>\s*</div>'
            template_content = re.sub(
                model_pattern,
                replacement,
                template_content,
                flags=re.DOTALL
            )
        
        return template_content

    def generate_interface(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a customized HTML interface based on configuration.

        Args:
            config: Configuration dictionary with the following keys:
                - interface_name: Name for the interface
                - custom_title: Optional custom page title
                - anti_hallucination_mode: 'strict', 'balanced', or 'off'
                - selected_model: Pre-selected AI model name
                - categories: List of allowed document categories

        Returns:
            Dictionary with generation results
        """
        try:
            # Validate configuration
            validation_result = self.validate_config(config)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"Configuration validation failed: {validation_result['error']}"
                }

            # Load base template
            template_content = self.load_template()

            # Apply customizations
            template_content = self.customize_html_template(template_content, config)
            template_content = self.remove_user_controls(template_content, config)
            template_content = self.inject_custom_javascript(template_content, config)

            # Generate filename
            interface_name = config.get('interface_name', 'custom_interface')
            safe_name = re.sub(r'[^a-zA-Z0-9_-]', '_', interface_name.lower())
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_name}_{timestamp}.html"
            filepath = os.path.join(self.output_dir, filename)

            # Save generated HTML
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(template_content)

            # Save metadata
            metadata = {
                'filename': filename,
                'filepath': filepath,
                'config': config,
                'generated_at': datetime.now().isoformat(),
                'file_size': os.path.getsize(filepath)
            }

            self.save_metadata(filename, metadata)

            logger.info(f"Generated custom interface: {filename}")

            return {
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'metadata': metadata
            }

        except Exception as e:
            logger.error(f"Error generating interface: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate the configuration for interface generation.

        Args:
            config: Configuration dictionary

        Returns:
            Validation result dictionary
        """
        errors = []

        # Validate interface name
        interface_name = config.get('interface_name', '').strip()
        if not interface_name:
            errors.append("Interface name is required")
        elif len(interface_name) > 100:
            errors.append("Interface name must be 100 characters or less")

        # Validate anti-hallucination mode
        anti_hallucination_mode = config.get('anti_hallucination_mode')
        if anti_hallucination_mode:
            valid_modes = self.get_anti_hallucination_modes().get('available_modes', [])
            if anti_hallucination_mode not in valid_modes:
                errors.append(f"Invalid anti-hallucination mode. Must be one of: {', '.join(valid_modes)}")

        # Validate selected model
        selected_model = config.get('selected_model')
        if selected_model:
            available_models = [m['name'] for m in self.get_available_models()]
            if selected_model not in available_models:
                errors.append(f"Invalid model selection. Available models: {', '.join(available_models)}")

        # Validate categories
        categories = config.get('categories', [])
        if categories:
            available_categories = self.get_available_categories()
            invalid_categories = [cat for cat in categories if cat not in available_categories]
            if invalid_categories:
                errors.append(f"Invalid categories: {', '.join(invalid_categories)}. Available: {', '.join(available_categories)}")

        return {
            'valid': len(errors) == 0,
            'error': '; '.join(errors) if errors else None
        }

    def save_metadata(self, filename: str, metadata: Dict[str, Any]):
        """Save metadata for generated interface."""
        metadata_file = os.path.join(self.output_dir, f"{filename}.meta.json")
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving metadata for {filename}: {str(e)}")

    def list_generated_interfaces(self) -> List[Dict[str, Any]]:
        """
        List all generated interfaces with their metadata.

        Returns:
            List of interface metadata dictionaries
        """
        interfaces = []

        try:
            if not os.path.exists(self.output_dir):
                return interfaces

            for filename in os.listdir(self.output_dir):
                if filename.endswith('.html'):
                    metadata_file = os.path.join(self.output_dir, f"{filename}.meta.json")

                    if os.path.exists(metadata_file):
                        try:
                            with open(metadata_file, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                                interfaces.append(metadata)
                        except Exception as e:
                            logger.error(f"Error loading metadata for {filename}: {str(e)}")
                    else:
                        # Create basic metadata for files without metadata
                        filepath = os.path.join(self.output_dir, filename)
                        stat = os.stat(filepath)
                        interfaces.append({
                            'filename': filename,
                            'filepath': filepath,
                            'generated_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            'file_size': stat.st_size,
                            'config': {}
                        })

            # Sort by generation date (newest first)
            interfaces.sort(key=lambda x: x.get('generated_at', ''), reverse=True)

        except Exception as e:
            logger.error(f"Error listing generated interfaces: {str(e)}")

        return interfaces

    def delete_interface(self, filename: str) -> Dict[str, Any]:
        """
        Delete a generated interface and its metadata.

        Args:
            filename: Name of the HTML file to delete

        Returns:
            Deletion result dictionary
        """
        try:
            html_path = os.path.join(self.output_dir, filename)
            metadata_path = os.path.join(self.output_dir, f"{filename}.meta.json")

            deleted_files = []

            # Delete HTML file
            if os.path.exists(html_path):
                os.remove(html_path)
                deleted_files.append(html_path)

            # Delete metadata file
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
                deleted_files.append(metadata_path)

            if not deleted_files:
                return {
                    'success': False,
                    'error': f"Interface file {filename} not found"
                }

            logger.info(f"Deleted interface: {filename}")

            return {
                'success': True,
                'deleted_files': deleted_files
            }

        except Exception as e:
            logger.error(f"Error deleting interface {filename}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
