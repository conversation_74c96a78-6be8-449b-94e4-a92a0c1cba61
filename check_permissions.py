#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to check and verify the admin_interface_generation permission configuration.
"""

import sys
import os
import sqlite3
import logging

# Add current directory to path
sys.path.append('.')

# Import required modules
try:
    from db_utils import get_db_connection
    from permissions import ensure_default_permission_groups, sync_new_module_permissions, get_dashboard_functions
    import user_management as um
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_permission_system():
    """Check the current state of the permission system."""
    print("=== Permission System Verification ===\n")
    
    try:
        # Initialize user management database first
        print("1. Initializing user management database...")
        if not um.init_user_db():
            print("   ❌ Failed to initialize user management database")
            return False
        else:
            print("   ✅ User management database initialized successfully")
        
        # Initialize permission groups
        print("\n2. Ensuring default permission groups...")
        if ensure_default_permission_groups():
            print("   ✅ Default permission groups initialized successfully")
        else:
            print("   ❌ Failed to initialize default permission groups")
            return False
        
        # Sync new module permissions
        print("\n3. Synchronizing module permissions...")
        if sync_new_module_permissions():
            print("   ✅ Module permissions synchronized successfully")
        else:
            print("   ❌ Failed to synchronize module permissions")
            return False
        
        # Check if admin_interface_generation function is defined
        print("\n4. Checking dashboard functions...")
        dashboard_functions = get_dashboard_functions()
        admin_interface_func = None
        for func in dashboard_functions:
            if func['name'] == 'admin_interface_generation':
                admin_interface_func = func
                break
        
        if admin_interface_func:
            print(f"   ✅ admin_interface_generation function found:")
            print(f"      - Name: {admin_interface_func['name']}")
            print(f"      - Description: {admin_interface_func['description']}")
            print(f"      - Category: {admin_interface_func['category']}")
            print(f"      - Default roles: {admin_interface_func['default_roles']}")
        else:
            print("   ❌ admin_interface_generation function not found in dashboard functions")
            return False
        
        # Check database permissions
        print("\n5. Checking database permissions...")
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check permission groups
        print("   Permission Groups:")
        cursor.execute('SELECT group_id, name, description FROM permission_groups ORDER BY name')
        groups = cursor.fetchall()
        for group in groups:
            print(f"      - {group[1]} (ID: {group[0]}): {group[2]}")
        
        # Check admin_interface_generation permissions
        print("\n   admin_interface_generation permissions:")
        cursor.execute('''
            SELECT pg.name, gp.function_name, gp.enabled 
            FROM permission_groups pg 
            JOIN group_permissions gp ON pg.group_id = gp.group_id 
            WHERE gp.function_name = ?
            ORDER BY pg.name
        ''', ('admin_interface_generation',))
        permissions = cursor.fetchall()
        
        if permissions:
            for perm in permissions:
                status = "✅ Enabled" if perm[2] else "❌ Disabled"
                print(f"      - {perm[0]}: {status}")
        else:
            print("      ❌ No admin_interface_generation permissions found in database")
            conn.close()
            return False
        
        # Check users with admin permissions
        print("\n6. Checking admin users...")
        cursor.execute('''
            SELECT u.username, u.role, ug.group_id, pg.name as group_name
            FROM users u
            LEFT JOIN user_groups ug ON u.id = ug.user_id
            LEFT JOIN permission_groups pg ON ug.group_id = pg.group_id
            WHERE u.role = 'admin' OR pg.name = 'Admin'
            ORDER BY u.username
        ''')
        admin_users = cursor.fetchall()
        
        if admin_users:
            for user in admin_users:
                group_info = f" (Group: {user[3]})" if user[3] else " (No group assigned)"
                print(f"      - {user[0]} (Role: {user[1]}){group_info}")
        else:
            print("      ⚠️  No admin users found")
        
        conn.close()
        
        print("\n=== Verification Complete ===")
        print("✅ Permission system appears to be properly configured!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error during verification: {str(e)}")
        logger.error(f"Permission verification failed: {str(e)}")
        return False

def test_permission_access():
    """Test permission access functionality."""
    print("\n=== Testing Permission Access ===\n")
    
    try:
        # Test the permission checking logic
        from flask import Flask
        from flask_login import AnonymousUserMixin
        
        # Create a mock user class for testing
        class MockUser:
            def __init__(self, role='admin', permissions=None):
                self.role = role
                self.is_authenticated = True
                self.permissions = permissions or []
            
            def has_dashboard_permission(self, function_name):
                """Mock permission checking."""
                if self.role == 'admin':
                    return True
                return function_name in self.permissions
        
        # Test admin user
        admin_user = MockUser(role='admin')
        has_permission = admin_user.has_dashboard_permission('admin_interface_generation')
        print(f"Admin user access to admin_interface_generation: {'✅ Allowed' if has_permission else '❌ Denied'}")
        
        # Test non-admin user without permission
        regular_user = MockUser(role='editor', permissions=[])
        has_permission = regular_user.has_dashboard_permission('admin_interface_generation')
        print(f"Editor user without permission: {'✅ Allowed' if has_permission else '❌ Denied'}")
        
        # Test non-admin user with permission
        authorized_user = MockUser(role='editor', permissions=['admin_interface_generation'])
        has_permission = authorized_user.has_dashboard_permission('admin_interface_generation')
        print(f"Editor user with permission: {'✅ Allowed' if has_permission else '❌ Denied'}")
        
        print("\n✅ Permission access testing completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error during permission access testing: {str(e)}")
        return False

if __name__ == '__main__':
    print("Starting permission system verification...\n")
    
    # Check permission system
    system_ok = check_permission_system()
    
    # Test permission access
    access_ok = test_permission_access()
    
    if system_ok and access_ok:
        print("\n🎉 All permission checks passed!")
        sys.exit(0)
    else:
        print("\n❌ Some permission checks failed!")
        sys.exit(1)
