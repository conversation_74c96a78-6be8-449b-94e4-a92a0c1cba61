#!/usr/bin/env python3
"""
Test script for the HTML Generator functionality.

This script tests the HTML generation system to ensure it works correctly
before deploying to production.
"""

import os
import sys
import json
from html_generator import HTMLGenerator

def test_html_generator():
    """Test the HTML generator functionality."""
    print("Testing HTML Generator...")
    
    # Initialize generator
    generator = HTMLGenerator()
    
    # Test 1: Get available options
    print("\n1. Testing configuration options...")
    try:
        categories = generator.get_available_categories()
        print(f"   Available categories: {categories}")
        
        models = generator.get_available_models()
        print(f"   Available models: {len(models)} models found")
        for model in models[:3]:  # Show first 3
            print(f"     - {model.get('display_name', model['name'])}")
        
        modes = generator.get_anti_hallucination_modes()
        print(f"   Anti-hallucination modes: {modes.get('available_modes', [])}")
        
    except Exception as e:
        print(f"   ERROR: {str(e)}")
        return False
    
    # Test 2: Validate configuration
    print("\n2. Testing configuration validation...")
    
    # Valid config
    valid_config = {
        'interface_name': 'Test Interface',
        'anti_hallucination_mode': 'strict',
        'selected_model': 'llama3.2:3b-instruct-q4_K_M',
        'categories': ['CANOPY']
    }
    
    validation = generator.validate_config(valid_config)
    print(f"   Valid config validation: {validation}")
    
    # Invalid config
    invalid_config = {
        'interface_name': '',  # Empty name
        'anti_hallucination_mode': 'invalid_mode',
        'categories': ['NONEXISTENT']
    }
    
    validation = generator.validate_config(invalid_config)
    print(f"   Invalid config validation: {validation}")
    
    # Test 3: Generate interface
    print("\n3. Testing interface generation...")
    
    test_config = {
        'interface_name': 'CANOPY Research Interface',
        'custom_title': 'ERDB CANOPY Research Portal',
        'anti_hallucination_mode': 'strict',
        'selected_model': 'llama3.2:3b-instruct-q4_K_M',
        'categories': ['CANOPY']
    }
    
    try:
        result = generator.generate_interface(test_config)
        
        if result['success']:
            print(f"   ✓ Interface generated successfully: {result['filename']}")
            print(f"   ✓ File size: {result['metadata']['file_size']} bytes")
            
            # Check if file exists
            if os.path.exists(result['filepath']):
                print(f"   ✓ File exists at: {result['filepath']}")
                
                # Read a sample of the file
                with open(result['filepath'], 'r', encoding='utf-8') as f:
                    content = f.read(500)  # First 500 chars
                    if 'CANOPY Research Interface' in content:
                        print("   ✓ Custom title found in generated HTML")
                    else:
                        print("   ⚠ Custom title not found in generated HTML")
                        
                    if 'CUSTOM_INTERFACE_CONFIG' in content:
                        print("   ✓ Custom JavaScript configuration found")
                    else:
                        print("   ⚠ Custom JavaScript configuration not found")
            else:
                print(f"   ✗ Generated file not found at: {result['filepath']}")
                return False
                
        else:
            print(f"   ✗ Generation failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"   ERROR: {str(e)}")
        return False
    
    # Test 4: List generated interfaces
    print("\n4. Testing interface listing...")
    
    try:
        interfaces = generator.list_generated_interfaces()
        print(f"   Found {len(interfaces)} generated interfaces")
        
        if interfaces:
            latest = interfaces[0]
            print(f"   Latest: {latest.get('filename', 'Unknown')}")
            print(f"   Config: {latest.get('config', {}).get('interface_name', 'Unknown')}")
            
    except Exception as e:
        print(f"   ERROR: {str(e)}")
        return False
    
    # Test 5: Clean up (optional)
    print("\n5. Cleaning up test files...")
    
    try:
        if result['success']:
            cleanup_result = generator.delete_interface(result['filename'])
            if cleanup_result['success']:
                print(f"   ✓ Test file deleted: {result['filename']}")
            else:
                print(f"   ⚠ Failed to delete test file: {cleanup_result['error']}")
                
    except Exception as e:
        print(f"   ERROR during cleanup: {str(e)}")
    
    print("\n✓ All tests completed successfully!")
    return True

def test_template_customization():
    """Test template customization functionality."""
    print("\nTesting template customization...")
    
    generator = HTMLGenerator()
    
    # Load template
    try:
        template_content = generator.load_template()
        print(f"   ✓ Template loaded: {len(template_content)} characters")
        
        # Test customization
        config = {
            'interface_name': 'Test Customization',
            'anti_hallucination_mode': 'balanced',
            'selected_model': 'llama3.2:3b-instruct-q4_K_M',
            'categories': ['MANUAL', 'RISE']
        }
        
        # Apply customizations
        customized = generator.customize_html_template(template_content, config)
        customized = generator.remove_user_controls(customized, config)
        customized = generator.inject_custom_javascript(customized, config)
        
        print(f"   ✓ Template customized: {len(customized)} characters")
        
        # Check for expected customizations
        if 'Test Customization' in customized:
            print("   ✓ Interface name customization applied")
        else:
            print("   ⚠ Interface name customization not found")
            
        if 'CUSTOM_INTERFACE_CONFIG' in customized:
            print("   ✓ Custom JavaScript configuration injected")
        else:
            print("   ⚠ Custom JavaScript configuration not found")
            
        if 'balanced' in customized:
            print("   ✓ Anti-hallucination mode configuration found")
        else:
            print("   ⚠ Anti-hallucination mode configuration not found")
            
        return True
        
    except Exception as e:
        print(f"   ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    print("HTML Generator Test Suite")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("templates/index.html"):
        print("ERROR: templates/index.html not found. Please run from the project root directory.")
        sys.exit(1)
    
    # Run tests
    success = True
    
    try:
        success &= test_template_customization()
        success &= test_html_generator()
        
        if success:
            print("\n🎉 All tests passed! The HTML generator is working correctly.")
        else:
            print("\n❌ Some tests failed. Please check the output above.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during testing: {str(e)}")
        sys.exit(1)
