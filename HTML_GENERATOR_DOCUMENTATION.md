# HTML Generator System Documentation

## Overview

The HTML Generator System is a backend service that creates customized chat interfaces based on the existing `index.html` template. It allows administrators to generate standalone HTML files with pre-configured settings for anti-hallucination mode, AI model selection, and document categories.

## Features

### Backend Components

1. **HTMLGenerator Service** (`html_generator.py`)
   - Template-based HTML generation
   - Configuration validation
   - File management and metadata tracking
   - Standalone HTML file creation

2. **API Endpoints** (in `app.py`)
   - `/api/generate-interface` - Generate custom interfaces
   - `/api/generated-interfaces` - List generated interfaces
   - `/api/generated-interfaces/<filename>` - Delete interfaces
   - `/api/interface-config-options` - Get configuration options
   - `/generated/<filename>` - Serve generated HTML files

3. **Admin Interface** (`templates/interface_generator.html`)
   - Web-based interface for generating custom HTML
   - Real-time configuration options
   - Interface management and preview

### Generated Interface Features

- **Pre-configured Settings**: Anti-hallucination mode, AI model, and categories
- **Standalone Operation**: Complete HTML files with embedded CSS/JavaScript
- **Preserved Functionality**: All existing chat features maintained
- **WCAG AA Compliance**: Accessibility standards maintained
- **Bootstrap 5 Styling**: Consistent with existing design system
- **Theme Toggle**: Dark/light mode support
- **Session Management**: Client name collection and device fingerprinting

## Configuration Options

### Anti-Hallucination Modes
- **Strict**: Only respond with information directly found in documents
- **Balanced**: Allow limited inference while citing sources
- **Off**: Allow more creative responses with external knowledge

### Available Models
- Dynamically loaded from `/api/available-models` endpoint
- Includes model display names, descriptions, and capabilities
- Fallback to default models if API unavailable

### Document Categories
- **CANOPY**: Forestry and ecosystem research
- **MANUAL**: Technical manuals and guides
- **RISE**: Research and innovation studies

## Usage Guide

### Admin Interface Access

1. Navigate to Admin Dashboard
2. Click on "Interface Generator" card
3. Requires `admin_interface_generation` permission

### Generating Custom Interfaces

1. **Interface Name** (Required)
   - Displayed as page title and header
   - Used for filename generation
   - Maximum 100 characters

2. **Custom Page Title** (Optional)
   - HTML page title override
   - Auto-generated if not provided

3. **Anti-Hallucination Mode** (Optional)
   - Pre-configure response behavior
   - Leave unselected for user choice

4. **AI Model** (Optional)
   - Pre-select specific AI model
   - Leave empty for user selection

5. **Document Categories** (Optional)
   - Limit interface to specific categories
   - Multiple selections allowed
   - Leave empty for all categories

### Generated File Structure

```
frontend/
├── interface_name_timestamp.html      # Generated HTML file
└── interface_name_timestamp.html.meta.json  # Metadata file
```

### File Naming Convention

- Format: `{safe_interface_name}_{timestamp}.html`
- Safe name: Alphanumeric characters, underscores, and hyphens only
- Timestamp: YYYYMMDD_HHMMSS format

## API Reference

### Generate Interface

```http
POST /api/generate-interface
Content-Type: application/json

{
  "interface_name": "CANOPY Research Portal",
  "custom_title": "ERDB CANOPY Research Interface",
  "anti_hallucination_mode": "strict",
  "selected_model": "llama3.2:3b-instruct-q4_K_M",
  "categories": ["CANOPY"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Interface 'CANOPY Research Portal' generated successfully",
  "filename": "canopy_research_portal_20250604_144702.html",
  "metadata": {
    "filename": "canopy_research_portal_20250604_144702.html",
    "filepath": "frontend/canopy_research_portal_20250604_144702.html",
    "config": {...},
    "generated_at": "2025-06-04T14:47:02.654682",
    "file_size": 110725
  }
}
```

### List Generated Interfaces

```http
GET /api/generated-interfaces
```

**Response:**
```json
{
  "success": true,
  "interfaces": [...],
  "count": 5
}
```

### Delete Interface

```http
DELETE /api/generated-interfaces/{filename}
```

### Get Configuration Options

```http
GET /api/interface-config-options
```

**Response:**
```json
{
  "success": true,
  "options": {
    "categories": ["CANOPY", "MANUAL", "RISE"],
    "models": [...],
    "anti_hallucination_modes": {...}
  }
}
```

### Serve Generated Interface

```http
GET /generated/{filename}
```

Returns the HTML file for direct browser access.

## Technical Implementation

### Template Customization Process

1. **Load Base Template**: Read `templates/index.html`
2. **Apply Customizations**:
   - Replace page title and interface name
   - Modify category selection (hidden input or limited dropdown)
   - Update anti-hallucination mode display
   - Replace model selector with pre-configured info
3. **Inject Custom JavaScript**: Add configuration and validation logic
4. **Save to Frontend Directory**: Write complete standalone HTML file

### JavaScript Configuration

Generated interfaces include a `CUSTOM_INTERFACE_CONFIG` object:

```javascript
window.CUSTOM_INTERFACE_CONFIG = {
    antiHallucinationMode: 'strict',
    selectedModel: 'llama3.2:3b-instruct-q4_K_M',
    categories: ["CANOPY"],
    isCustomInterface: true
};
```

### Security Considerations

- **Permission-based Access**: Requires admin role and specific permission
- **Input Validation**: All configuration parameters validated
- **Path Traversal Protection**: Filename sanitization
- **CSRF Protection**: Standard Flask CSRF tokens

## File Management

### Metadata Tracking

Each generated interface includes a `.meta.json` file with:
- Original configuration
- Generation timestamp
- File size and path
- Interface name and settings

### Cleanup and Maintenance

- Interfaces can be deleted through admin interface
- Both HTML and metadata files removed together
- No automatic cleanup (manual management required)

## Error Handling

### Validation Errors
- Empty interface name
- Invalid anti-hallucination mode
- Non-existent model selection
- Invalid category selection

### Generation Errors
- Template loading failures
- File system permissions
- Disk space issues

### Runtime Errors
- Missing dependencies
- Configuration file issues
- Network connectivity (for model loading)

## Best Practices

1. **Naming Conventions**: Use descriptive interface names
2. **Category Limitation**: Specify categories for focused interfaces
3. **Model Selection**: Choose appropriate models for use case
4. **Regular Cleanup**: Remove unused generated interfaces
5. **Testing**: Verify generated interfaces work independently
6. **Documentation**: Document custom interfaces for users

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure user has `admin_interface_generation` permission
2. **Template Not Found**: Verify `templates/index.html` exists
3. **Generation Fails**: Check file system permissions for `frontend/` directory
4. **Interface Not Working**: Verify all static assets are accessible

### Debug Information

- Check application logs for detailed error messages
- Verify configuration options API responses
- Test template loading independently
- Validate generated HTML syntax

## Future Enhancements

### Planned Features
- **Template Variants**: Multiple base templates
- **Custom Styling**: Interface-specific CSS customization
- **Bulk Generation**: Generate multiple interfaces at once
- **Version Control**: Track interface versions and changes
- **Export/Import**: Share interface configurations

### Integration Opportunities
- **CI/CD Pipeline**: Automated interface generation
- **User Roles**: Role-specific interface generation
- **Analytics**: Track generated interface usage
- **Backup System**: Automated interface backups
