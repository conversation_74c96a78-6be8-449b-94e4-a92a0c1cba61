<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>ERDB Comprehensive Research Portal</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Leaflet CSS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>

    <!-- Design System CSS -->
    <link rel="stylesheet" href="/static/css/design-system.css">

    <!-- Welcome Screen CSS -->
    <link rel="stylesheet" href="/static/css/welcome-screen.css">

    <!-- Comprehensive Theme Fixes -->
    <link rel="stylesheet" href="/static/css/theme-fixes.css">

    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    <!-- Voice Interface CSS -->
    <link rel="stylesheet" href="/static/css/voice-interface.css">

    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>

    <!-- Model Selector Styles -->
    <style>
        .model-info {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            border: 1px solid #e9ecef;
            font-size: 0.875rem;
        }

        .model-description {
            display: block;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }

        .model-size {
            display: block;
            color: #495057;
            font-weight: 500;
        }

        .model-capability {
            display: inline-block;
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 0.125rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.25rem;
        }

        /* Dark mode styles for model info */
        .dark .model-info {
            background-color: #374151;
            border-color: #4b5563;
        }

        .dark .model-description {
            color: #9ca3af;
        }

        .dark .model-size {
            color: #d1d5db;
        }

        .dark .model-capability {
            background-color: #1e3a8a;
            color: #93c5fd;
        }

        /* Model selector loading state */
        .model-selector-loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Model selector tooltip */
        .model-tooltip {
            position: relative;
        }

        .model-tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 1000;
            margin-bottom: 0.25rem;
        }

        .model-tooltip:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: #333;
            z-index: 1000;
        }
    </style>

    <!-- Script Loading Management -->
    <script>
        // Prevent duplicate script loading
        window.loadedScripts = window.loadedScripts || new Set();
    </script>

    <!-- Shared Utilities -->
    <script src="/static/js/utilities.js" onload="window.loadedScripts.add('/static/js/utilities.js')"></script>
    <script>
        // Suppress Iterable-related console errors (likely from browser extensions)
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('iterable') || message.includes('Iterable')) {
                // Suppress Iterable-related errors as they're likely from browser extensions
                return;
            }
            originalConsoleError.apply(console, args);
        };

        // Configure marked.js to properly handle links and other markdown elements
        marked.setOptions({
            breaks: true,  // Add line breaks
            gfm: true,     // Enable GitHub Flavored Markdown
            headerIds: false,
            mangle: false,
            sanitize: false, // Allow HTML in markdown
            renderer: (function() {
                const renderer = new marked.Renderer();

                // Override the link renderer to add target="_blank" and other attributes
                renderer.link = function(href, title, text) {
                    const link = marked.Renderer.prototype.link.call(this, href, title, text);
                    return link.replace('<a ', '<a target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline" ');
                };

                return renderer;
            })()
        });

        // We'll use the standardized theme toggle functionality from utilities.js
        let isDarkMode = localStorage.getItem('theme') === 'dark';

        // Initialize theme using utilities.js on page load
        document.addEventListener('DOMContentLoaded', function() {
            // This will set the correct theme classes
            DMSUtils.initDarkMode();
        });

        // Generate a more robust device fingerprint
        function generateDeviceFingerprint() {
            // Collect various device characteristics
            const screenInfo = `${window.screen.width}x${window.screen.height}x${window.screen.colorDepth}`;
            const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const language = navigator.language;
            const platform = navigator.platform;
            const userAgent = navigator.userAgent;
            const cpuCores = navigator.hardwareConcurrency || 'unknown';
            const deviceMemory = navigator.deviceMemory || 'unknown';
            const doNotTrack = navigator.doNotTrack || 'unknown';
            const cookiesEnabled = navigator.cookieEnabled;

            // Add canvas fingerprinting (creates a unique image based on hardware/software)
            let canvasData = 'unknown';
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = 200;
                canvas.height = 50;

                // Draw text with specific styling
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillStyle = '#1a73e8';
                ctx.fillText('ERDB Knowledge Products', 2, 2);

                // Add a colored rectangle
                ctx.fillStyle = '#e8731a';
                ctx.fillRect(100, 25, 80, 20);

                // Get canvas data
                canvasData = canvas.toDataURL().substring(0, 100);
            } catch (e) {
                console.error('Canvas fingerprinting failed:', e);
            }

            // Combine all values
            const fingerprint = `${screenInfo}-${timeZone}-${language}-${platform}-${cpuCores}-${deviceMemory}-${doNotTrack}-${cookiesEnabled}-${canvasData}-${userAgent.substring(0, 50)}`;

            // Create a hash of the fingerprint
            let hash = 0;
            for (let i = 0; i < fingerprint.length; i++) {
                const char = fingerprint.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32bit integer
            }

            // Combine hash with base64 encoding for better uniqueness
            const base64 = btoa(fingerprint).replace(/[^a-zA-Z0-9]/g, '');
            return `${Math.abs(hash).toString(16)}-${base64.substring(0, 16)}`;
        }

        // Store client name, session information, and device fingerprint
        let clientName = localStorage.getItem('clientName') || '';
        let sessionId = localStorage.getItem('sessionId') || '';
        let sessionStart = localStorage.getItem('sessionStart') || '';
        let deviceFingerprint = localStorage.getItem('deviceFingerprint') || generateDeviceFingerprint();

        // Save the fingerprint
        localStorage.setItem('deviceFingerprint', deviceFingerprint);

        // Model selection variables
        let availableModels = [];
        let selectedModel = localStorage.getItem('selectedModel') || '';

        // Function to load available models
        async function loadAvailableModels() {
            try {
                const modelSelector = document.getElementById('model-selector');
                const modelInfo = document.getElementById('model-info');

                // Show loading state
                modelSelector.classList.add('model-selector-loading');
                modelSelector.innerHTML = '<option value="">Loading models...</option>';

                const response = await fetch('/api/available-models');
                const result = await response.json();

                if (result.success && result.models) {
                    availableModels = result.models;
                    populateModelSelector(result.models, result.default_model);

                    // Set default model if none selected
                    if (!selectedModel && result.default_model) {
                        selectedModel = result.default_model;
                        localStorage.setItem('selectedModel', selectedModel);
                    }

                    // Update model info display
                    updateModelInfo();
                } else {
                    throw new Error(result.error || 'Failed to load models');
                }
            } catch (error) {
                console.error('Error loading models:', error);

                // Fallback to default models
                const fallbackModels = [
                    {
                        name: 'llama3.1:8b-instruct-q4_K_M',
                        display_name: 'Llama 3.1 8B',
                        description: 'Fast, efficient model for general queries',
                        capability: 'General Purpose',
                        size_formatted: '4.9 GB'
                    }
                ];

                availableModels = fallbackModels;
                populateModelSelector(fallbackModels, 'llama3.1:8b-instruct-q4_K_M');

                if (!selectedModel) {
                    selectedModel = 'llama3.1:8b-instruct-q4_K_M';
                    localStorage.setItem('selectedModel', selectedModel);
                }

                updateModelInfo();
            } finally {
                const modelSelector = document.getElementById('model-selector');
                modelSelector.classList.remove('model-selector-loading');
            }
        }

        // Function to populate model selector dropdown
        function populateModelSelector(models, defaultModel) {
            const modelSelector = document.getElementById('model-selector');
            modelSelector.innerHTML = '';

            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '- Select AI Model -';
            modelSelector.appendChild(defaultOption);

            // Add model options
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = `${model.display_name} (${model.size_formatted})`;
                option.setAttribute('data-description', model.description);
                option.setAttribute('data-capability', model.capability);
                option.setAttribute('data-size', model.size_formatted);

                // Mark as selected if it matches stored selection or default
                if (model.name === selectedModel || (model.name === defaultModel && !selectedModel)) {
                    option.selected = true;
                    selectedModel = model.name;
                }

                modelSelector.appendChild(option);
            });

            // Add event listener for model selection changes
            modelSelector.addEventListener('change', handleModelSelection);
        }

        // Function to handle model selection changes
        function handleModelSelection(event) {
            const selectedValue = event.target.value;

            if (selectedValue) {
                selectedModel = selectedValue;
                localStorage.setItem('selectedModel', selectedModel);
                console.log('Model selected:', selectedModel);
            } else {
                selectedModel = '';
                localStorage.removeItem('selectedModel');
            }

            updateModelInfo();
        }

        // Function to update model info display
        function updateModelInfo() {
            const modelInfo = document.getElementById('model-info');
            const modelDescription = document.getElementById('model-description');
            const modelSize = document.getElementById('model-size');

            if (!selectedModel) {
                modelInfo.style.display = 'none';
                return;
            }

            // Find the selected model in available models
            const model = availableModels.find(m => m.name === selectedModel);

            if (model) {
                modelDescription.innerHTML = `
                    <span class="model-capability">${model.capability}</span><br>
                    ${model.description}
                `;
                modelSize.textContent = `Size: ${model.size_formatted}`;
                modelInfo.style.display = 'block';
            } else {
                modelInfo.style.display = 'none';
            }
        }

        // Function to save chat messages to localStorage with device fingerprint
        function saveChatHistory() {
            const chatBox = document.getElementById('chat-box');
            if (chatBox && chatBox.innerHTML.trim() !== '') {
                try {
                    // Save chat history with device fingerprint as part of the key
                    const historyKey = `chatHistory_${deviceFingerprint}`;
                    localStorage.setItem(historyKey, chatBox.innerHTML);

                    // Also save to the regular key for backward compatibility
                    localStorage.setItem('chatHistory', chatBox.innerHTML);

                    // Save timestamp of last update
                    localStorage.setItem(`chatHistoryUpdated_${deviceFingerprint}`, new Date().toISOString());

                    console.log('Chat history saved successfully');
                } catch (error) {
                    console.error('Error saving chat history:', error);

                    // If localStorage is full, try to clear old data
                    if (error.name === 'QuotaExceededError') {
                        try {
                            // Remove any old chat histories that don't match current fingerprint
                            for (let i = 0; i < localStorage.length; i++) {
                                const key = localStorage.key(i);
                                if (key && key.startsWith('chatHistory_') && !key.includes(deviceFingerprint)) {
                                    localStorage.removeItem(key);
                                }
                            }

                            // Try saving again
                            localStorage.setItem(`chatHistory_${deviceFingerprint}`, chatBox.innerHTML);
                        } catch (retryError) {
                            console.error('Failed to save chat history after cleanup:', retryError);
                        }
                    }
                }
            }
        }

        // Function to load chat messages from localStorage with device fingerprint
        function loadChatHistory() {
            const chatBox = document.getElementById('chat-box');
            if (!chatBox) return false;

            try {
                // Try to load chat history using device fingerprint first
                const historyKey = `chatHistory_${deviceFingerprint}`;
                let savedHistory = localStorage.getItem(historyKey);

                // If not found, try the regular key for backward compatibility
                if (!savedHistory) {
                    savedHistory = localStorage.getItem('chatHistory');

                    // If we found history in the old format, migrate it to the new format
                    if (savedHistory) {
                        localStorage.setItem(historyKey, savedHistory);
                        console.log('Migrated chat history to device-specific storage');
                    }
                }

                if (savedHistory && savedHistory.trim() !== '') {
                    // Create a temporary div to sanitize the content
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = savedHistory;

                    // Check if the content has actual messages
                    const hasMessages = tempDiv.querySelectorAll('.flex.justify-start, .flex.justify-end').length > 0;

                    if (hasMessages) {
                        chatBox.innerHTML = savedHistory;
                        chatBox.scrollTop = chatBox.scrollHeight;
                        console.log('Chat history loaded successfully');

                        // Add voice controls to restored AI messages
                        setTimeout(() => {
                            try {
                                if (typeof VoiceInterface !== 'undefined' && VoiceInterface.state && VoiceInterface.state.isSupported) {
                                    console.log('🎤 Adding voice controls to restored messages...');

                                    const aiMessages = chatBox.querySelectorAll('.welcome-message');
                                    console.log(`📝 Found ${aiMessages.length} restored messages`);

                                    aiMessages.forEach((message, index) => {
                                        // Skip if already has voice controls
                                        if (message.querySelector('.speech-control-btn')) {
                                            console.log(`⏭️ Message ${index} already has voice controls`);
                                            return;
                                        }

                                        const messageId = `restored_ai_${index}_${Date.now()}`;
                                        const messageText = VoiceInterface.extractMessageText(message);

                                        console.log(`🎤 Adding voice controls to restored message ${index}: ${messageId}`);

                                        if (messageText && messageText.length > 0) {
                                            VoiceInterface.addSpeechControls(message, messageId, messageText);
                                        } else {
                                            console.warn(`⚠️ No text extracted from restored message ${index}`);
                                        }
                                    });
                                } else {
                                    console.log('⚠️ VoiceInterface not available for restored messages');
                                }
                            } catch (error) {
                                console.error('❌ Error adding voice controls to restored messages:', error);
                            }
                        }, 500); // Increased delay for restored content

                        // Store the timestamp of when history was loaded
                        localStorage.setItem(`chatHistoryLoaded_${deviceFingerprint}`, new Date().toISOString());

                        return true;
                    } else {
                        console.log('Saved chat history exists but contains no messages');
                    }
                } else {
                    console.log('No saved chat history found');
                }
            } catch (error) {
                console.error('Error loading chat history:', error);

                // If there was an error, try to recover by clearing potentially corrupted history
                try {
                    const historyKey = `chatHistory_${deviceFingerprint}`;
                    localStorage.removeItem(historyKey);
                    localStorage.removeItem('chatHistory');
                    console.log('Cleared potentially corrupted chat history');
                } catch (clearError) {
                    console.error('Error clearing corrupted history:', clearError);
                }
            }

            return false;
        }



        // Function to generate varied personalized greetings using API - Enhanced for Phase 2
        async function getPersonalizedGreeting(name, greetingType = 'response') {
            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Get current local time and timezone for time-based greetings
                const now = new Date();
                const localTime = now.toISOString();
                const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

                // Determine time of day
                const hour = now.getHours();
                let timeOfDay = 'afternoon'; // default
                if (hour >= 5 && hour < 12) {
                    timeOfDay = 'morning';
                } else if (hour >= 12 && hour < 18) {
                    timeOfDay = 'afternoon';
                } else {
                    timeOfDay = 'evening';
                }

                const response = await fetch('/api/greeting', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({
                        client_name: name,
                        context: {
                            greeting_type: greetingType,
                            session_id: sessionId,
                            device_fingerprint: deviceFingerprint,
                            client_name: name,
                            local_time: localTime,
                            timezone: timezone,
                            time_of_day: timeOfDay
                        }
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Store greeting context for analytics
                    if (result.greeting_data) {
                        localStorage.setItem('lastGreetingContext', JSON.stringify({
                            session_type: result.greeting_data.session_type,
                            time_of_day: result.greeting_data.time_of_day,
                            greeting_type: result.greeting_data.template_type,
                            timestamp: now.toISOString()
                        }));
                    }
                    return result.greeting;
                } else {
                    // Fallback to time-aware hardcoded greeting
                    return getTimeAwareFallbackGreeting(name, timeOfDay);
                }
            } catch (error) {
                console.error('Error getting personalized greeting:', error);
                return getTimeAwareFallbackGreeting(name);
            }
        }

        // Enhanced time-aware fallback greeting function - Phase 2
        function getTimeAwareFallbackGreeting(name, timeOfDay = null) {
            // Determine time of day if not provided
            if (!timeOfDay) {
                const hour = new Date().getHours();
                if (hour >= 5 && hour < 12) {
                    timeOfDay = 'morning';
                } else if (hour >= 12 && hour < 18) {
                    timeOfDay = 'afternoon';
                } else {
                    timeOfDay = 'evening';
                }
            }

            const timeBasedGreetings = {
                morning: [
                    `Good morning, ${name}!`,
                    `Morning, ${name}! How can I help you today?`,
                    `Good morning, ${name}! Ready to explore some knowledge?`,
                    `Hello ${name}, hope you're having a great start to your day!`
                ],
                afternoon: [
                    `Good afternoon, ${name}!`,
                    `Afternoon, ${name}! How's your day going?`,
                    `Good afternoon, ${name}! What can I help you with?`,
                    `Hello ${name}, hope you're having a productive afternoon!`
                ],
                evening: [
                    `Good evening, ${name}!`,
                    `Evening, ${name}! How can I assist you tonight?`,
                    `Good evening, ${name}! Working late?`,
                    `Hello ${name}, hope you're having a pleasant evening!`
                ]
            };

            const greetings = timeBasedGreetings[timeOfDay] || timeBasedGreetings.afternoon;
            return greetings[Math.floor(Math.random() * greetings.length)];
        }

        // Legacy fallback greeting function for backward compatibility
        function getFallbackGreeting(name) {
            return getTimeAwareFallbackGreeting(name);
        }

        // Helper function to add personalized greeting to response content
        async function addPersonalizedGreeting(contentElementId, clientName, greetingType = 'response') {
            try {
                const greeting = await getPersonalizedGreeting(clientName, greetingType);
                const contentElement = document.getElementById(contentElementId);
                if (contentElement) {
                    const greetingParagraph = document.createElement('p');
                    greetingParagraph.innerHTML = `<strong>${greeting}</strong>`;
                    contentElement.insertBefore(greetingParagraph, contentElement.firstChild);
                }
            } catch (error) {
                console.error('Error adding personalized greeting:', error);
                // Add fallback greeting
                const contentElement = document.getElementById(contentElementId);
                if (contentElement) {
                    const greetingParagraph = document.createElement('p');
                    greetingParagraph.innerHTML = `<strong>${getFallbackGreeting(clientName)}</strong>`;
                    contentElement.insertBefore(greetingParagraph, contentElement.firstChild);
                }
            }
        }

        // Initialize dark class on document element
        if (isDarkMode) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }

        function toggleTheme() {
            // Use the standardized theme toggle from utilities.js
            isDarkMode = !isDarkMode;
            DMSUtils.toggleDarkMode(isDarkMode);

            // Apply theme changes immediately
            applyTheme();

            // Also apply theme to chat content
            applyThemeToRestoredContent();
        }

        function applyTheme() {
            // We'll use a simpler approach that matches the admin dashboard
            if (isDarkMode) {
                document.body.classList.add('bg-gray-800');
                document.body.classList.remove('bg-gray-100');

                // Update container backgrounds
                document.querySelectorAll('.bg-white').forEach(el => {
                    // Skip form inputs and selects to keep them white with black text
                    if (el.tagName !== 'INPUT' && el.tagName !== 'SELECT' && el.tagName !== 'TEXTAREA') {
                        el.classList.add('bg-gray-700', 'text-white');
                        el.classList.remove('bg-white');
                    }
                });

                // Update text colors
                document.querySelectorAll('.text-gray-800').forEach(el => {
                    // Skip form inputs and selects to keep text black
                    if (el.tagName !== 'INPUT' && el.tagName !== 'SELECT' && el.tagName !== 'TEXTAREA') {
                        el.classList.add('text-gray-100');
                        el.classList.remove('text-gray-800');
                    }
                });

                document.querySelectorAll('.text-gray-700').forEach(el => {
                    // Skip form inputs and selects to keep text black
                    if (el.tagName !== 'INPUT' && el.tagName !== 'SELECT' && el.tagName !== 'TEXTAREA') {
                        el.classList.add('text-gray-300');
                        el.classList.remove('text-gray-700');
                    }
                });

                // Update borders
                document.querySelectorAll('.border-gray-200').forEach(el => {
                    el.classList.add('border-gray-600');
                    el.classList.remove('border-gray-200');
                });

                document.querySelectorAll('.bg-gray-50').forEach(el => {
                    // Skip form inputs and selects
                    if (el.tagName !== 'INPUT' && el.tagName !== 'SELECT' && el.tagName !== 'TEXTAREA') {
                        el.classList.add('bg-gray-600');
                        el.classList.remove('bg-gray-50');
                    }
                });

                // Update table styling for dark mode
                document.querySelectorAll('table.table').forEach(el => {
                    el.classList.add('dark-table');
                });

                document.querySelectorAll('table th').forEach(el => {
                    el.style.backgroundColor = '#2d3748';
                    el.style.color = '#e2e8f0';
                });

                document.querySelectorAll('table td').forEach(el => {
                    el.style.borderColor = '#4a5568';
                });

                // Ensure form elements keep black text in dark mode
                document.querySelectorAll('input, select, textarea').forEach(el => {
                    el.classList.add('text-gray-800');
                    el.classList.remove('text-white', 'text-gray-100', 'text-gray-300');
                });
            } else {
                document.body.classList.remove('bg-gray-800');
                document.body.classList.add('bg-gray-100');

                // Update container backgrounds
                document.querySelectorAll('.bg-gray-700').forEach(el => {
                    el.classList.remove('bg-gray-700', 'text-white');
                    el.classList.add('bg-white');
                });

                // Update text colors
                document.querySelectorAll('.text-gray-100').forEach(el => {
                    el.classList.remove('text-gray-100');
                    el.classList.add('text-gray-800');
                });

                document.querySelectorAll('.text-gray-300').forEach(el => {
                    el.classList.remove('text-gray-300');
                    el.classList.add('text-gray-700');
                });

                // Update borders
                document.querySelectorAll('.border-gray-600').forEach(el => {
                    el.classList.remove('border-gray-600');
                    el.classList.add('border-gray-200');
                });

                document.querySelectorAll('.bg-gray-600').forEach(el => {
                    el.classList.remove('bg-gray-600');
                    el.classList.add('bg-gray-50');
                });

                // Reset table styling for light mode
                document.querySelectorAll('table.table').forEach(el => {
                    el.classList.remove('dark-table');
                });

                document.querySelectorAll('table th').forEach(el => {
                    el.style.backgroundColor = '#f8f9fa';
                    el.style.color = '#212529';
                });

                document.querySelectorAll('table td').forEach(el => {
                    el.style.borderColor = '#dee2e6';
                });
            }
        }

        // Function to specifically apply theme to restored chat content
        function applyThemeToRestoredContent() {
            const chatBox = document.getElementById('chat-box');
            if (!chatBox) return;

            // Process all message containers in the chat box
            const messageContainers = chatBox.querySelectorAll('.flex.justify-start, .flex.justify-end');

            messageContainers.forEach(container => {
                // Apply theme to each message based on current theme
                if (isDarkMode) {
                    // For user messages (blue background)
                    container.querySelectorAll('.bg-gradient-to-br.from-blue-50.to-blue-100').forEach(el => {
                        // Keep the gradient but adjust colors for dark mode
                        el.classList.remove('from-blue-50', 'to-blue-100');
                        el.classList.add('from-blue-900', 'to-blue-800');
                        el.classList.add('border-blue-700');
                        el.classList.remove('border-blue-200');
                    });

                    // For AI messages (green background)
                    container.querySelectorAll('.bg-gradient-to-br.from-green-50.to-green-100').forEach(el => {
                        // Keep the gradient but adjust colors for dark mode
                        el.classList.remove('from-green-50', 'to-green-100');
                        el.classList.add('from-green-900', 'to-green-800');
                        el.classList.add('border-green-700');
                        el.classList.remove('border-green-200');
                    });

                    // For error messages (red background)
                    container.querySelectorAll('.bg-red-50').forEach(el => {
                        el.classList.remove('bg-red-50');
                        el.classList.add('bg-red-900');
                        el.classList.add('border-red-700');
                        el.classList.remove('border-red-200');
                    });

                    // Update text colors
                    container.querySelectorAll('.text-gray-800').forEach(el => {
                        if (el.tagName !== 'INPUT' && el.tagName !== 'SELECT' && el.tagName !== 'TEXTAREA') {
                            el.classList.add('text-gray-100');
                            el.classList.remove('text-gray-800');
                        }
                    });

                    container.querySelectorAll('.text-gray-700').forEach(el => {
                        if (el.tagName !== 'INPUT' && el.tagName !== 'SELECT' && el.tagName !== 'TEXTAREA') {
                            el.classList.add('text-gray-300');
                            el.classList.remove('text-gray-700');
                        }
                    });

                    // Update borders
                    container.querySelectorAll('.border-gray-200').forEach(el => {
                        el.classList.add('border-gray-600');
                        el.classList.remove('border-gray-200');
                    });

                    // Update backgrounds
                    container.querySelectorAll('.bg-gray-50').forEach(el => {
                        if (el.tagName !== 'INPUT' && el.tagName !== 'SELECT' && el.tagName !== 'TEXTAREA') {
                            el.classList.add('bg-gray-600');
                            el.classList.remove('bg-gray-50');
                        }
                    });

                    // Update details/summary elements
                    container.querySelectorAll('details').forEach(el => {
                        el.querySelectorAll('.border-gray-200').forEach(borderEl => {
                            borderEl.classList.add('border-gray-600');
                            borderEl.classList.remove('border-gray-200');
                        });

                        el.querySelectorAll('.text-gray-500').forEach(textEl => {
                            textEl.classList.add('text-gray-300');
                            textEl.classList.remove('text-gray-500');
                        });
                    });

                    // Update links
                    container.querySelectorAll('a.text-blue-600').forEach(el => {
                        el.classList.add('text-blue-400');
                        el.classList.remove('text-blue-600');
                    });
                } else {
                    // Reverse dark mode changes if needed
                    // For user messages
                    container.querySelectorAll('.bg-gradient-to-br.from-blue-900.to-blue-800').forEach(el => {
                        el.classList.remove('from-blue-900', 'to-blue-800');
                        el.classList.add('from-blue-50', 'to-blue-100');
                        el.classList.remove('border-blue-700');
                        el.classList.add('border-blue-200');
                    });

                    // For AI messages
                    container.querySelectorAll('.bg-gradient-to-br.from-green-900.to-green-800').forEach(el => {
                        el.classList.remove('from-green-900', 'to-green-800');
                        el.classList.add('from-green-50', 'to-green-100');
                        el.classList.remove('border-green-700');
                        el.classList.add('border-green-200');
                    });

                    // For error messages
                    container.querySelectorAll('.bg-red-900').forEach(el => {
                        el.classList.remove('bg-red-900');
                        el.classList.add('bg-red-50');
                        el.classList.remove('border-red-700');
                        el.classList.add('border-red-200');
                    });

                    // Update text colors
                    container.querySelectorAll('.text-gray-100').forEach(el => {
                        el.classList.remove('text-gray-100');
                        el.classList.add('text-gray-800');
                    });

                    container.querySelectorAll('.text-gray-300').forEach(el => {
                        el.classList.remove('text-gray-300');
                        el.classList.add('text-gray-700');
                    });

                    // Update borders
                    container.querySelectorAll('.border-gray-600').forEach(el => {
                        el.classList.remove('border-gray-600');
                        el.classList.add('border-gray-200');
                    });

                    // Update backgrounds
                    container.querySelectorAll('.bg-gray-600').forEach(el => {
                        el.classList.remove('bg-gray-600');
                        el.classList.add('bg-gray-50');
                    });

                    // Update links
                    container.querySelectorAll('a.text-blue-400').forEach(el => {
                        el.classList.remove('text-blue-400');
                        el.classList.add('text-blue-600');
                    });
                }
            });

            console.log('Theme applied to restored chat content');
        }

        // Apply theme on page load
        window.addEventListener('DOMContentLoaded', applyTheme);

        async function sendQuery() {
            const category = document.getElementById('category').value;
            const query = document.getElementById('query').value.trim();
            if (!category || !query) {
                alert('Please select a category and enter a query.');
                return;
            }

            // Check if a model is selected
            if (!selectedModel) {
                alert('Please select an AI model before submitting your query.');
                return;
            }

            // Get the selected anti-hallucination mode
            const antiHallucinationMode = document.querySelector('input[name="anti_hallucination_mode"]:checked').value;

            // Get model display name for UI
            const selectedModelObj = availableModels.find(m => m.name === selectedModel);
            const modelDisplayName = selectedModelObj ? selectedModelObj.display_name : selectedModel;

            const chatBox = document.getElementById('chat-box');
            const userMessage = document.createElement('div');
            userMessage.className = 'flex justify-start mb-4 opacity-0 transition-opacity duration-500';
            userMessage.innerHTML = `
                <div class="max-w-2xl bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 shadow-sm border border-blue-200">
                    <div class="flex items-center mb-2">
                        <svg class="user-message-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <p class="text-sm font-medium text-gray-700">${clientName || 'You'} <span class="text-xs text-gray-500 ml-2">${new Date().toLocaleTimeString()}</span></p>
                    </div>
                    <p class="text-gray-800">${query}</p>
                    <div class="mt-1 text-xs text-gray-500">
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                            antiHallucinationMode === 'strict' ? 'bg-blue-100 text-blue-800' :
                            antiHallucinationMode === 'balanced' ? 'bg-green-100 text-green-800' :
                            'bg-red-100 text-red-800'
                        }">
                            Mode: ${
                                antiHallucinationMode === 'strict' ? 'Strict' :
                                antiHallucinationMode === 'balanced' ? 'Balanced' :
                                'Creative'
                            }
                        </span>
                        <span class="inline-flex items-center px-2 py-0.5 ml-1 rounded text-xs font-medium bg-purple-100 text-purple-800">
                            Model: ${modelDisplayName}
                        </span>
                    </div>
                </div>
            `;
            chatBox.appendChild(userMessage);
            setTimeout(() => userMessage.classList.remove('opacity-0'), 50);

            // Save chat history after adding the user message
            saveChatHistory();

            const typingIndicator = document.createElement('div');
            typingIndicator.className = 'flex justify-end mb-4 opacity-0 transition-opacity duration-500';
            typingIndicator.innerHTML = `
                <div class="max-w-2xl bg-gray-50 rounded-lg p-4 shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <svg class="typing-indicator-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        <p class="text-gray-600 animate-pulse">EAIA is thinking...</p>
                    </div>
                </div>
            `;
            chatBox.appendChild(typingIndicator);
            setTimeout(() => typingIndicator.classList.remove('opacity-0'), 50);
            chatBox.scrollTop = chatBox.scrollHeight;

            try {
                // Get CSRF token from meta tag
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                const response = await fetch(`/query/${category}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({
                        query,
                        anti_hallucination_mode: antiHallucinationMode,
                        selected_model: selectedModel,
                        client_name: clientName,
                        device_fingerprint: deviceFingerprint,
                        session_id: sessionId,
                        session_start: sessionStart
                    })
                });
                const result = await response.json();

                // Store session information if provided by the server
                if (result.session_id) {
                    sessionId = result.session_id;
                    localStorage.setItem('sessionId', sessionId);
                }

                if (result.session_start) {
                    sessionStart = result.session_start;
                    localStorage.setItem('sessionStart', sessionStart);
                }

                chatBox.removeChild(typingIndicator);

                if (result.error) {
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'flex justify-end mb-4 opacity-0 transition-opacity duration-500';

                    // Check if it's a CSRF error and provide helpful message
                    let errorText = result.error;
                    let helpText = '';

                    if (result.error.includes('CSRF') || result.error.includes('csrf')) {
                        errorText = 'Security token expired. Please try your request again.';
                        helpText = '<p class="text-sm text-gray-600 mt-2">If this problem persists, please refresh the page.</p>';
                    }

                    errorMessage.innerHTML = `
                        <div class="max-w-2xl bg-red-50 rounded-lg p-4 shadow-sm border border-red-200">
                            <div class="flex items-center mb-2">
                                <svg class="error-message-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <p class="text-sm font-medium text-gray-700">Error <span class="text-xs text-gray-500 ml-2">${new Date().toLocaleTimeString()}</span></p>
                            </div>
                            <p class="text-red-600">${errorText}</p>
                            ${helpText}
                        </div>
                    `;
                    chatBox.appendChild(errorMessage);
                    setTimeout(() => errorMessage.classList.remove('opacity-0'), 50);
                } else {
                    const botMessage = document.createElement('div');
                    botMessage.className = 'flex justify-end mb-4 opacity-0 transition-opacity duration-500';

                    // Process sources with better formatting and grouping
                    let sourcesHtml = '';
                    if (result.sources && result.sources.length) {
                        // Group sources by document name
                        const groupedSources = {};

                        result.sources.forEach(source => {
                            if (typeof source !== 'string') {
                                const displayName = source.display_name || source.source || 'Unknown';

                                if (!groupedSources[displayName]) {
                                    groupedSources[displayName] = {
                                        type: source.type || 'unknown',
                                        pages: [],
                                        file_path: source.file_path,
                                        original_url: source.original_url,
                                        image_count: 0,
                                        link_count: 0,
                                        sources: []
                                    };
                                }

                                // Add page if it exists and isn't already in the array
                                if (source.page && !groupedSources[displayName].pages.includes(source.page)) {
                                    groupedSources[displayName].pages.push(source.page);
                                }

                                // Sum up image and link counts
                                groupedSources[displayName].image_count += (source.image_count || 0);
                                groupedSources[displayName].link_count += (source.link_count || 0);

                                // Store the original source object
                                groupedSources[displayName].sources.push(source);
                            }
                        });

                        sourcesHtml = `
                            <details class="mt-3 border-t border-gray-200 dark:border-gray-600 pt-2" open>
                                <summary class="text-sm font-medium text-gray-500 dark:text-gray-300 cursor-pointer hover:text-gray-700 dark:hover:text-gray-100 flex items-center">
                                    <svg class="details-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Sources (${Object.keys(groupedSources).length})
                                </summary>
                                <div class="mt-2 space-y-3">
                        `;

                        // Sort sources by name for consistent display
                        const sortedSourceNames = Object.keys(groupedSources).sort();

                        sortedSourceNames.forEach(sourceName => {
                            const sourceGroup = groupedSources[sourceName];
                            try {
                                let displayName = '';

                                // Handle different source types
                                if (sourceGroup.type === 'pdf') {
                                    // For PDFs, create a link to the file if file_path is available
                                    // Always prioritize original_url for PDFs if available
                                    if (sourceGroup.original_url) {
                                        displayName = `<a href="${sourceGroup.original_url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline font-medium" title="View PDF">${sourceName}</a>`;
                                    } else if (sourceGroup.file_path) {
                                        displayName = `<a href="${sourceGroup.file_path}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline font-medium" title="View PDF">${sourceName}</a>`;
                                    } else {
                                        displayName = sourceName;
                                    }
                                } else if (sourceGroup.type === 'url') {
                                    // For URLs, link to the original URL
                                    if (sourceGroup.original_url) {
                                        displayName = `<a href="${sourceGroup.original_url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline font-medium truncate-url" title="${sourceGroup.original_url}">${sourceName}</a>`;
                                    } else if (sourceName.startsWith && sourceName.startsWith('http')) {
                                        displayName = `<a href="${sourceName}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline font-medium truncate-url" title="${sourceName}">${sourceName}</a>`;
                                    } else {
                                        displayName = sourceName;
                                    }
                                } else {
                                    // Default case
                                    displayName = sourceName;
                                }

                                // Create a badge for the source type
                                const typeBadge = `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">${sourceGroup.type}</span>`;

                                // Create badges for image, table, and link counts if available
                                let countBadges = '';
                                if (sourceGroup.image_count > 0) {
                                    countBadges += ` <span class="inline-flex items-center px-2 py-0.5 ml-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-200">${sourceGroup.image_count} images</span>`;
                                }
                                if (sourceGroup.table_count > 0) {
                                    countBadges += ` <span class="inline-flex items-center px-2 py-0.5 ml-1 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-700 dark:text-purple-200">${sourceGroup.table_count} tables</span>`;
                                }
                                if (sourceGroup.link_count > 0) {
                                    countBadges += ` <span class="inline-flex items-center px-2 py-0.5 ml-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-200">${sourceGroup.link_count} links</span>`;
                                }

                                // Format page numbers
                                let pageInfo = '';
                                if (sourceGroup.pages && sourceGroup.pages.length > 0) {
                                    // Sort page numbers numerically
                                    const sortedPages = sourceGroup.pages.sort((a, b) => a - b);
                                    pageInfo = `<div class="text-sm text-gray-500 dark:text-gray-400 mt-1">Pages: ${sortedPages.join(', ')}</div>`;
                                }

                                // Create the source card
                                sourcesHtml += `
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                                        <div class="flex flex-wrap items-center gap-2">
                                            <div class="font-medium">${displayName}</div>
                                            ${typeBadge}
                                            ${countBadges}
                                        </div>
                                        ${pageInfo}
                                    </div>
                                `;
                            } catch (error) {
                                console.error("Error processing source group:", error, sourceGroup);
                                sourcesHtml += `<div class="bg-red-50 p-2 rounded">Error displaying source</div>`;
                            }
                        });

                        sourcesHtml += '</div></details>';
                    }

                    // Process images with better display (for backward compatibility)
                    let imagesHtml = '';
                    if (result.images && result.images.length && (!result.url_images || !result.pdf_images)) {
                        // Only use this if the new fields are not available
                        imagesHtml = `
                            <details class="mt-3 border-t border-gray-200 dark:border-gray-600 pt-2">
                                <summary class="text-sm font-medium text-gray-500 dark:text-gray-300 cursor-pointer hover:text-gray-700 dark:hover:text-gray-100 flex items-center">
                                    <svg class="details-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    Related Images (${result.images.length})
                                </summary>
                                <div class="grid grid-cols-2 gap-3 mt-3">
                                    ${result.images.join('')}
                                </div>
                            </details>
                        `;
                    }

                    // Process PDF links with better display
                    let pdfLinksHtml = '';
                    if (result.pdf_links && result.pdf_links.length) {
                        pdfLinksHtml = `
                            <details class="mt-3 border-t border-gray-200 dark:border-gray-600 pt-2">
                                <summary class="text-sm font-medium text-gray-500 dark:text-gray-300 cursor-pointer hover:text-gray-700 dark:hover:text-gray-100 flex items-center">
                                    <svg class="details-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                    </svg>
                                    Download Links (${result.pdf_links.length})
                                </summary>
                                <ul class="list-disc ml-6 mt-2 text-sm text-gray-600 dark:text-gray-200 space-y-1">
                                    ${result.pdf_links.map(link => {
                                        const displayUrl = link.length > 60 ? link.substring(0, 57) + '...' : link;
                                        return `<li><a href="${link}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline" title="${link}">${displayUrl}</a></li>`;
                                    }).join('')}
                                </ul>
                            </details>
                        `;
                    }

                    // Process tables with better display
                    let tablesHtml = '';
                    if (result.tables && result.tables.length) {
                        tablesHtml = `
                            <details class="mt-3 border-t border-gray-200 dark:border-gray-600 pt-2">
                                <summary class="text-sm font-medium text-gray-500 dark:text-gray-300 cursor-pointer hover:text-gray-700 dark:hover:text-gray-100 flex items-center">
                                    <svg class="details-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                    </svg>
                                    Extracted Tables (${result.tables.length})
                                </summary>
                                <div class="space-y-3 mt-3">
                                    ${result.tables.join('')}
                                </div>
                            </details>
                        `;
                    }

                    // Process URL JPG/PNG images to display at the top (excluding PNG logos)
                    let urlImagesHtml = '';
                    if (result.url_images && result.url_images.length) {
                        urlImagesHtml = `
                            <div class="grid grid-cols-2 gap-3 mb-4">
                                ${result.url_images.join('')}
                            </div>
                        `;
                    }

                    // Process PDF-extracted images for the "Related Images" section
                    let pdfImagesHtml = '';
                    if (result.pdf_images && result.pdf_images.length) {
                        pdfImagesHtml = `
                            <details class="mt-3 border-t border-gray-200 dark:border-gray-600 pt-2">
                                <summary class="text-sm font-medium text-gray-500 dark:text-gray-300 cursor-pointer hover:text-gray-700 dark:hover:text-gray-100 flex items-center">
                                    <svg class="details-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    Related Images from PDFs (${result.pdf_images.length})
                                </summary>
                                <div class="grid grid-cols-2 gap-3 mt-3">
                                    ${result.pdf_images.join('')}
                                </div>
                            </details>
                        `;
                    }

                    // Combine everything into the bot message
                    botMessage.innerHTML = `
                        <div class="welcome-message">
                            <div class="welcome-message-header">
                                <svg class="welcome-message-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                                <h3 class="welcome-message-title">EAIA</h3>
                                <span class="welcome-message-time">${new Date().toLocaleTimeString()}</span>
                            </div>
                            <div class="welcome-message-content">
                                ${urlImagesHtml}

                                <!-- Document Thumbnails Section -->
                                ${result.document_thumbnails && result.document_thumbnails.length > 0 ? `
                                    <div class="document-thumbnails-container mb-4">
                                        ${result.document_thumbnails.join('')}
                                    </div>
                                ` : ''}

                                <div id="response-content-${Date.now()}">
                                    ${marked.parse(result.answer)}
                                </div>

                                <!-- Follow-up Questions Section -->
                                ${result.followup_questions && result.followup_questions.length > 0 ? `
                                    <div class="mt-4 border-t border-gray-200 dark:border-gray-600 pt-3">
                                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Follow-up Questions:</p>
                                        <div class="flex flex-wrap gap-2">
                                            ${result.followup_questions.map((question, index) => `
                                                <button
                                                    class="px-3 py-2 bg-blue-50 hover:bg-blue-100 text-blue-700 text-sm rounded-lg border border-blue-200 transition-colors"
                                                    onclick="document.getElementById('query').value = '${question.replace(/'/g, "\\'")}'; document.getElementById('category').value = '${category}'; sendQuery();">
                                                    ${question.replace(/\*(.*?)\*/g, '<em>$1</em>')}
                                                </button>
                                            `).join('')}
                                        </div>
                                    </div>
                                ` : ''}

                                ${sourcesHtml}${pdfImagesHtml}${tablesHtml}${pdfLinksHtml}
                            </div>
                        </div>
                    `;
                    chatBox.appendChild(botMessage);
                    setTimeout(() => botMessage.classList.remove('opacity-0'), 50);

                    // Add personalized greeting if client name is available
                    if (clientName) {
                        const responseContentId = botMessage.querySelector('[id^="response-content-"]').id;
                        addPersonalizedGreeting(responseContentId, clientName);
                    }

                    // Add voice controls to the new AI response
                    setTimeout(() => {
                        try {
                            if (typeof VoiceInterface !== 'undefined' && VoiceInterface.state && VoiceInterface.state.isSupported) {
                                const messageId = `ai_response_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                                const messageText = VoiceInterface.extractMessageText(botMessage);

                                console.log('🎤 Adding voice controls to AI response:', messageId);

                                if (messageText && messageText.length > 0) {
                                    VoiceInterface.addSpeechControls(botMessage, messageId, messageText);

                                    // Handle auto-play if enabled
                                    VoiceInterface.handleAutoPlay(botMessage, messageId);
                                } else {
                                    console.warn('⚠️ No text extracted for voice controls');
                                }
                            } else {
                                console.log('⚠️ VoiceInterface not available or not supported');
                            }
                        } catch (error) {
                            console.error('❌ Error adding voice controls:', error);
                        }
                    }, 200); // Increased delay to ensure message is fully rendered

                    // Save chat history after adding the bot message
                    saveChatHistory();
                }
            } catch (error) {
                chatBox.removeChild(typingIndicator);
                const errorMessage = document.createElement('div');
                errorMessage.className = 'flex justify-end mb-4 opacity-0 transition-opacity duration-500';
                errorMessage.innerHTML = `
                    <div class="max-w-2xl bg-red-50 rounded-lg p-4 shadow-sm border border-red-200">
                        <div class="flex items-center mb-2">
                            <svg class="error-message-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <p class="text-sm font-medium text-gray-700">Error <span class="text-xs text-gray-500 ml-2">${new Date().toLocaleTimeString()}</span></p>
                        </div>
                        <p class="text-red-600">Error: ${error.message}</p>
                    </div>
                `;
                chatBox.appendChild(errorMessage);
                setTimeout(() => errorMessage.classList.remove('opacity-0'), 50);

                // Save chat history even when there's an error
                saveChatHistory();
            }

            chatBox.scrollTop = chatBox.scrollHeight;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendQuery();
            }
        }

        // Function to show the client name modal
        function showClientNameModal() {
            const modal = document.getElementById('clientNameModal');
            const nameInput = document.getElementById('clientNameInput');

            // If we have a stored name, pre-fill the input
            if (clientName) {
                nameInput.value = clientName;
            }

            // Show the modal
            modal.classList.remove('hidden');

            // Focus the input field
            setTimeout(() => nameInput.focus(), 100);
        }

        // Function to save the client name
        function saveClientName() {
            const nameInput = document.getElementById('clientNameInput');
            clientName = nameInput.value.trim();

            if (clientName) {
                localStorage.setItem('clientName', clientName);
                document.getElementById('clientNameModal').classList.add('hidden');

                // Update the logout button with the user's name
                const logoutButton = document.getElementById('logoutButton');
                logoutButton.textContent = `Logout (${clientName})`;

                // Clear the input for next time
                nameInput.value = '';

                // Add a welcome message to the chat
                const chatBox = document.getElementById('chat-box');
                const welcomeMessage = document.createElement('div');
                welcomeMessage.className = 'welcome-message';
                welcomeMessage.innerHTML = `
                    <div class="welcome-message-header">
                        <svg class="welcome-message-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        <h3 class="welcome-message-title">EAIA</h3>
                        <span class="welcome-message-time">${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="welcome-message-content" id="welcome-content-${Date.now()}">
                        <p>To get started:</p>
                        <ol>
                            <li>Select a category from the dropdown menu below</li>
                            <li>Type your question in the text box</li>
                            <li>Press Enter or click the arrow button to submit</li>
                        </ol>
                        <p>My answers will be based on the available ERDB Knowledge Products in the selected category. How can I help you today?</p>
                    </div>
                `;
                chatBox.appendChild(welcomeMessage);

                // Add personalized greeting to welcome message
                const welcomeContentId = welcomeMessage.querySelector('[id^="welcome-content-"]').id;
                addPersonalizedGreeting(welcomeContentId, clientName, 'welcome');

                // Add voice controls to welcome message
                setTimeout(() => {
                    try {
                        if (typeof VoiceInterface !== 'undefined' && VoiceInterface.state && VoiceInterface.state.isSupported) {
                            const messageId = `welcome_${Date.now()}`;
                            const messageText = VoiceInterface.extractMessageText(welcomeMessage);

                            console.log('🎤 Adding voice controls to welcome message:', messageId);

                            if (messageText && messageText.length > 0) {
                                VoiceInterface.addSpeechControls(welcomeMessage, messageId, messageText);
                            } else {
                                console.warn('⚠️ No text extracted from welcome message');
                            }
                        } else {
                            console.log('⚠️ VoiceInterface not available for welcome message');
                        }
                    } catch (error) {
                        console.error('❌ Error adding voice controls to welcome message:', error);
                    }
                }, 300);

                saveChatHistory();
            } else {
                alert('Please enter your name to continue.');
            }
        }

        // Function to end the current session
        function endSession() {
            if (sessionId) {
                // Get CSRF token from meta tag
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Make a non-blocking request to close the session
                fetch(`/admin/session/${sessionId}/close`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    }
                }).catch(error => console.error('Error closing session:', error));

                // Clear session data from localStorage
                localStorage.removeItem('sessionId');
                localStorage.removeItem('sessionStart');
                localStorage.removeItem('clientName'); // Also clear client name
                sessionId = '';
                sessionStart = '';
                clientName = '';
            }
        }

        // Initialize the page and show the modal when the DOM is loaded
        window.addEventListener('DOMContentLoaded', function() {
            // Check if this is a page refresh or a new session
            const isPageRefresh = performance.navigation ?
                (performance.navigation.type === 1) :
                (window.performance && window.performance.getEntriesByType('navigation')[0]?.type === 'reload');

            // Load available models first
            loadAvailableModels();

            // First, load saved chat history if available (before applying theme)
            const historyLoaded = loadChatHistory();

            // Then apply theme to ensure it doesn't interfere with loaded content
            applyTheme();

            // Update the logout button with the user's name if available
            if (clientName) {
                const logoutButton = document.getElementById('logoutButton');
                if (logoutButton) {
                    logoutButton.textContent = `Logout (${clientName})`;
                }

                // Only add welcome message if no history was loaded or this isn't a page refresh
                if (!historyLoaded) {
                    // Add a welcome back message to the chat
                    const chatBox = document.getElementById('chat-box');
                    if (chatBox) {
                        const welcomeMessage = document.createElement('div');
                        welcomeMessage.className = 'welcome-message';
                        welcomeMessage.innerHTML = `
                            <div class="welcome-message-header">
                                <svg class="welcome-message-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                                <h3 class="welcome-message-title">EAIA</h3>
                                <span class="welcome-message-time">${new Date().toLocaleTimeString()}</span>
                            </div>
                            <div class="welcome-message-content">
                                <p><strong>${getPersonalizedGreeting(clientName)}</strong></p>
                                <p>Welcome back! To get started:</p>
                                <ol>
                                    <li>Select a category from the dropdown menu below</li>
                                    <li>Type your question in the text box</li>
                                    <li>Press Enter or click the arrow button to submit</li>
                                </ol>
                                <p>My answers will be based on the available ERDB Knowledge Products in the selected category. How can I help you today?</p>
                            </div>
                        `;
                        chatBox.appendChild(welcomeMessage);

                        // Add voice controls to welcome back message
                        setTimeout(() => {
                            try {
                                if (typeof VoiceInterface !== 'undefined' && VoiceInterface.state && VoiceInterface.state.isSupported) {
                                    const messageId = `welcome_back_${Date.now()}`;
                                    const messageText = VoiceInterface.extractMessageText(welcomeMessage);

                                    console.log('🎤 Adding voice controls to welcome back message:', messageId);

                                    if (messageText && messageText.length > 0) {
                                        VoiceInterface.addSpeechControls(welcomeMessage, messageId, messageText);
                                    } else {
                                        console.warn('⚠️ No text extracted from welcome back message');
                                    }
                                } else {
                                    console.log('⚠️ VoiceInterface not available for welcome back message');
                                }
                            } catch (error) {
                                console.error('❌ Error adding voice controls to welcome back message:', error);
                            }
                        }, 300);

                        saveChatHistory();
                    }
                } else if (isPageRefresh) {
                    console.log('Page was refreshed, chat history restored');

                    // Reapply theme to the restored chat content
                    applyThemeToRestoredContent();
                }
            } else {
                // If no client name is stored, show the name input modal
                setTimeout(showClientNameModal, 500); // Slight delay to ensure the page is loaded
            }

            // Add event listener for beforeunload to save chat history before page refresh
            window.addEventListener('beforeunload', function() {
                saveChatHistory();
            });
        });

        // Function to clear all session data and cookies (explicit logout)
        function clearAllSessionData() {
            try {
                // Store the device fingerprint before clearing
                const currentFingerprint = deviceFingerprint;

                // Clear all localStorage items except device fingerprint
                localStorage.removeItem('clientName');
                localStorage.removeItem('sessionId');
                localStorage.removeItem('sessionStart');
                localStorage.removeItem('chatHistory'); // Clear regular chat history

                // Clear device fingerprint-specific chat history and related data
                if (currentFingerprint) {
                    localStorage.removeItem(`chatHistory_${currentFingerprint}`);
                    localStorage.removeItem(`chatHistoryUpdated_${currentFingerprint}`);
                }

                // Clear any other potential chat history items that might exist
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.startsWith('chatHistory_') || key.startsWith('chatHistoryUpdated_'))) {
                        keysToRemove.push(key);
                    }
                }

                // Remove the collected keys (doing this separately to avoid issues with changing localStorage during iteration)
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                });

                // Don't clear the device fingerprint to maintain user identification across sessions
                // localStorage.removeItem('deviceFingerprint');

                // Reset variables
                clientName = '';
                sessionId = '';
                sessionStart = '';

                // Clear the chat box
                const chatBox = document.getElementById('chat-box');
                if (chatBox) {
                    chatBox.innerHTML = '';
                }

                // Call the server endpoint to close the session
                if (sessionId) {
                    if (navigator.sendBeacon) {
                        // Use sendBeacon for more reliable delivery
                        // Create FormData since sendBeacon doesn't support JSON content-type
                        const formData = new FormData();
                        formData.append('session_id', sessionId);
                        navigator.sendBeacon('/clear_session', formData);
                    } else {
                        // Fallback to fetch with keepalive
                        fetch('/clear_session', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ session_id: sessionId }),
                            keepalive: true
                        }).catch(error => console.error('Error clearing session:', error));
                    }
                }

                // Verify that all chat history has been cleared
                const verifyCleared = localStorage.getItem(`chatHistory_${currentFingerprint}`);
                if (verifyCleared) {
                    console.warn('Failed to clear chat history on first attempt, trying again...');
                    // Try one more time with a different approach
                    localStorage.removeItem(`chatHistory_${currentFingerprint}`);
                }

                console.log('All chat history and session data cleared successfully');
                return true;
            } catch (error) {
                console.error('Error clearing session data:', error);
                return false;
            }
        }

        // Handle browser close event (but not page refresh)
        // The pagehide event with persisted=false indicates the page is being unloaded from memory
        window.addEventListener('pagehide', function(event) {
            // Save chat history regardless of whether it's a refresh or close
            saveChatHistory();

            // If persisted is false, the page is being unloaded (closed, not navigated away from)
            if (!event.persisted) {
                // Only close the session on the server, but don't clear local storage
                // This allows chat history to persist if the user reopens the app
                if (sessionId) {
                    try {
                        if (navigator.sendBeacon) {
                            // Create FormData since sendBeacon doesn't support JSON content-type
                            const formData = new FormData();
                            formData.append('session_id', sessionId);
                            const sent = navigator.sendBeacon('/clear_session', formData);
                            if (!sent) {
                                console.warn('Failed to send session close beacon');
                            }
                        } else {
                            fetch('/clear_session', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ session_id: sessionId }),
                                keepalive: true
                            }).catch(error => console.error('Error closing session:', error));
                        }
                    } catch (error) {
                        console.error('Error during page unload handling:', error);
                    }
                }
            }
            // If persisted is true, it's just being put in the bfcache (like when refreshing)
            // so we just save the chat history but don't close the session
        });

        // Additional visibility change handler to better detect when the page is hidden/shown
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'hidden') {
                // Page is being hidden (tab switch, minimize, etc.)
                saveChatHistory();
            } else if (document.visibilityState === 'visible') {
                // Page is becoming visible again
                // No need to reload history here as it's already in the DOM
            }
        });
    </script>
    <style>
        body {
            background-color: #f3f4f6; /* bg-gray-100 */
        }
        .dark body {
            background-color: #1f2937; /* dark:bg-gray-800 */
        }

        /* Modal styles */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 50;
        }

        /* Ensure form elements always have black text on white background in dark mode */
        .dark input, .dark select, .dark textarea {
            color: #1f2937 !important; /* text-gray-800 */
            background-color: #ffffff !important; /* bg-white */
        }
        /* Make sure the body and html take full height */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        /* Ensure the container takes full height */
        .container {
            min-height: calc(100vh - 2rem); /* Account for padding */
        }

        /* Chat box should take available space but be scrollable */
        #chat-box {
            min-height: 300px; /* Minimum height to ensure visibility */
            max-height: calc(100vh - 250px); /* Maximum height to leave room for input */
        }

        /* URL truncation */
        .truncate-url {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
            vertical-align: middle;
        }

        @media (max-width: 640px) {
            .truncate-url {
                max-width: 150px;
            }

            /* Adjust chat box height on mobile */
            #chat-box {
                max-height: calc(100vh - 300px);
            }
        }

        /* Ensure images in prose content are responsive */
        .prose img {
            max-width: 100%;
            height: auto;
        }

        /* Style for cover images */
        .cover-image {
            order: -1; /* Display cover images first */
            border: 2px solid #3b82f6; /* Blue border to highlight cover images */
            border-radius: 0.5rem;
            padding: 0.25rem;
            background-color: #eff6ff; /* Light blue background */
        }

        /* Make the grid container use flexbox ordering */
        .document-thumbnails-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
        }

        /* Table styling */
        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
            border-collapse: collapse;
        }

        .table-sm th,
        .table-sm td {
            padding: 0.3rem;
            font-size: 0.875rem;
        }

        .table-bordered {
            border: 1px solid #dee2e6;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
        }

        .table-responsive {
            display: block;
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Dark mode table styling */
        .dark .table {
            color: #e2e8f0;
            border-color: #4a5568;
        }

        .dark .table-bordered th,
        .dark .table-bordered td {
            border-color: #4a5568;
        }

        /* Make sure table headers stand out */
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .dark .table th {
            background-color: #2d3748;
        }

        /* Scientific name styling - LLM-generated markdown italics */
        em {
            font-style: italic;
            font-weight: normal;
        }

        /* Ensure scientific names are visible in both themes */
        .dark em {
            color: inherit;
        }

        /* Scientific names in follow-up questions */
        button em {
            font-style: italic;
            color: inherit;
        }
    </style>
</head>
<body class="welcome-container">
    <!-- Client Name Modal -->
    <div id="clientNameModal" class="modal-backdrop hidden">
        <div class="modal-content">
            <h2 class="modal-title">Welcome to ERDB Knowledge Hub</h2>
            <p class="modal-text">This system provides information based on ERDB Knowledge Products.</p>
            <p class="modal-text">Please enter your name to continue:</p>
            <div>
                <input type="text" id="clientNameInput" placeholder="Your name"
                    class="modal-input"
                    onkeypress="if(event.key === 'Enter') saveClientName()">
            </div>
            <div class="modal-info">
                <p class="modal-info-title">How to use:</p>
                <p class="modal-info-text">Select a category, ask questions about ERDB knowledge products, and get answers with citations and related resources.</p>
                <p class="modal-info-text">Your chat history will be preserved when you refresh the page, but will be completely cleared when you click Logout.</p>
            </div>

            <!-- Privacy Notice -->
            <div class="modal-info">
                <p class="modal-info-title">Privacy Notice:</p>
                <p class="modal-info-text">This application collects your approximate geographic location based on your IP address to:</p>
                <ul class="modal-info-list">
                    <li>Analyze the geographic distribution of our users</li>
                    <li>Improve our services based on regional usage patterns</li>
                    <li>Provide aggregated analytics to system administrators</li>
                </ul>
                <p class="modal-info-text">Your location data is stored alongside your device identifier but is not linked to personally identifiable information. This data is not shared with third parties.</p>
            </div>

            <button onclick="saveClientName()" class="modal-button">
                Continue
            </button>
        </div>
    </div>

    <!-- Prevent form submission when pressing Enter in the URL bar -->
    <form onsubmit="return false;">
    <div class="welcome-main">
        <div class="welcome-card">
            <!-- Header Section -->
            <div class="welcome-header">
                <h1 class="welcome-title">Multi-Category Research Hub</h1>
                <p class="welcome-subtitle">Powered by ERDB AI Assistant</p>
            </div>

            <!-- Top Navigation -->
            <div class="top-nav">
                <div class="nav-links">
                    <a href="{{ url_for('admin_dashboard') }}" class="nav-link">&larr; Admin Dashboard</a>
                </div>
                <div class="nav-links">
                    <button onclick="clearAllSessionData(); window.location.reload();" class="logout-button" title="Logout and clear chat history">
                        <span id="logoutButton">Logout</span>
                    </button>
                    <button id="voice-settings-toggle" onclick="VoiceInterface.toggleSettingsPanel()" class="voice-settings-button" title="Voice Settings">
                        <span class="voice-settings-icon">🔊</span>
                    </button>
                    <button id="theme-toggle" onclick="toggleTheme()" class="theme-toggle">
                        <span id="theme-icon" class="text-xl">☀️</span>
                    </button>
                </div>
            </div>

            <!-- Voice Settings Panel -->
            <div id="voice-settings-panel" class="voice-settings-panel hidden">
                <div class="voice-settings-header">
                    <h4 class="voice-settings-title">Voice Settings</h4>
                    <button onclick="VoiceInterface.toggleSettingsPanel()" class="voice-settings-close" aria-label="Close voice settings">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="voice-settings-content">
                    <!-- TTS Settings -->
                    <div class="voice-setting-group">
                        <label for="voice-select" class="voice-setting-label">Voice</label>
                        <select id="voice-select" class="voice-setting-select">
                            <option value="">Loading voices...</option>
                        </select>
                    </div>

                    <div class="voice-setting-group">
                        <label for="speech-rate" class="voice-setting-label">Speech Rate</label>
                        <div class="voice-slider-container">
                            <input type="range" id="speech-rate" class="voice-setting-slider" min="0.5" max="2.0" step="0.1" value="1.0">
                            <span id="speech-rate-value" class="voice-slider-value">1.0x</span>
                        </div>
                    </div>

                    <div class="voice-setting-group">
                        <label for="speech-volume" class="voice-setting-label">Volume</label>
                        <div class="voice-slider-container">
                            <input type="range" id="speech-volume" class="voice-setting-slider" min="0" max="100" step="5" value="80">
                            <span id="speech-volume-value" class="voice-slider-value">80%</span>
                        </div>
                    </div>

                    <div class="voice-setting-group">
                        <label class="voice-setting-checkbox">
                            <input type="checkbox" id="auto-play-responses">
                            <span class="voice-setting-checkbox-text">Auto-play new responses</span>
                        </label>
                    </div>

                    <!-- STT Settings (Phase 2) -->
                    <div class="voice-setting-group voice-stt-settings" style="display: none;">
                        <h5 class="voice-setting-subtitle">Voice Input (Coming Soon)</h5>
                        <label for="voice-language" class="voice-setting-label">Language</label>
                        <select id="voice-language" class="voice-setting-select" disabled>
                            <option value="en-US">English (US)</option>
                            <option value="en-GB">English (UK)</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Chat Area -->
            <div class="chat-area">
                <!-- Chat Box - Takes most of the screen -->
                <div id="chat-box" class="chat-box"></div>

                <!-- Input Area -->
                <div class="input-area">
                    <div class="input-grid">
                        
                    <div class="input-group">
                        <label for="category" class="input-label">Category</label>
                        <select id="category" class="input-select">
                            <option value="">- Select Category -</option>
                            <option value="CANOPY">CANOPY</option>
<option value="RISE">RISE</option>
                        </select>
                        <div class="alert alert-info">
                            This interface is limited to: CANOPY, RISE
                        </div>
                    </div>



                        <div class="input-group">
                            <label for="query" class="input-label">Your Question</label>
                            <div class="input-with-button">
                                <textarea id="query" rows="1" placeholder="Enter your question about ERDB knowledge..."
                                    class="input-textarea"
                                    onkeypress="handleKeyPress(event)"></textarea>
                                <button onclick="sendQuery()" class="input-button">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Anti-hallucination mode selector -->
                    
                <div class="mode-selector">
                    <div class="mode-header">
                        <svg class="mode-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                        <h3 class="mode-title">Anti-Hallucination Mode: <strong>Off</strong></h3>
                    </div>
                    <div class="alert alert-info">
                        <strong>Pre-configured:</strong> Allow more creative responses with external knowledge
                    </div>
                    <input type="hidden" name="anti_hallucination_mode" value="off">
                </div>
                        <div class="mode-options">
                            <label class="mode-option">
                                <input type="radio" name="anti_hallucination_mode" value="strict" checked>
                                <span class="mode-option-label">Strict</span>
                                <span class="mode-option-desc">(Facts only)</span>
                            </label>
                            <label class="mode-option">
                                <input type="radio" name="anti_hallucination_mode" value="balanced">
                                <span class="mode-option-label">Balanced</span>
                                <span class="mode-option-desc">(Some inferences)</span>
                            </label>
                            <label class="mode-option">
                                <input type="radio" name="anti_hallucination_mode" value="off">
                                <span class="mode-option-label">Creative</span>
                                <span class="mode-option-desc">(Allow external knowledge)</span>
                            </label>
                        </div>
                    </div>

					   <div class="input-group">
                            <label for="model-selector" class="input-label">AI Model</label>
                            <select id="model-selector" class="input-select">
                                <option value="">Loading models...</option>
                            </select>
                            <div id="model-info" class="model-info" style="display: none;">
                                <span id="model-description" class="model-description"></span>
                                <span id="model-size" class="model-size"></span>
                            </div>
                        </div>


                </div>
            </div>
        </div>
    </div>
    </form>

    <!-- Leaflet JavaScript for maps -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <!-- Chat Maps Module -->
    <script src="/static/js/chat-maps.js"></script>

    <!-- Voice Interface Module -->
    <script src="/static/js/voice-interface.js"></script>

    <script>
        // Initialize chat maps functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure models are loaded if not already done
            if (availableModels.length === 0) {
                loadAvailableModels();
            }

            // Add chat maps integration to the query submission
            const originalSubmitQuery = window.submitQuery;
            if (originalSubmitQuery) {
                window.submitQuery = function() {
                    // Call original function
                    const result = originalSubmitQuery.apply(this, arguments);

                    // Add map analysis after response is received
                    setTimeout(() => {
                        const chatBox = document.getElementById('chat-box');
                        if (chatBox) {
                            // Look for the most recent bot response using the correct CSS classes
                            const botMessages = chatBox.querySelectorAll('.welcome-message');
                            if (botMessages.length > 0) {
                                const lastBotMessage = botMessages[botMessages.length - 1];
                                const responseContent = lastBotMessage.querySelector('.welcome-message-content');

                                if (responseContent) {
                                    const responseText = responseContent.textContent || responseContent.innerText;
                                    const responseId = `response_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

                                    // Add response ID for map targeting
                                    responseContent.setAttribute('data-response-id', responseId);

                                    // Extract sources from the response for better location context
                                    const sources = [];
                                    const sourceElements = lastBotMessage.querySelectorAll('.source-item');
                                    sourceElements.forEach(sourceEl => {
                                        const sourceText = sourceEl.textContent || sourceEl.innerText;
                                        sources.push({ text: sourceText });
                                    });

                                    // Analyze for map display
                                    ChatMaps.analyzeAndDisplayMap(responseText, responseId, sources);
                                }
                            }
                        }
                    }, 1500); // Wait for response to be fully rendered

                    return result;
                };
            }
        });
    </script>

        <script>
            // Custom Interface Configuration
            window.CUSTOM_INTERFACE_CONFIG = {
                antiHallucinationMode: 'off',
                selectedModel: '',
                categories: ["CANOPY", "RISE"],
                isCustomInterface: true
            };
            
            // Override model selection and anti-hallucination mode
            document.addEventListener('DOMContentLoaded', function() {
                // Pre-configure anti-hallucination mode
                const antiHallucinationRadio = document.querySelector('input[name="anti_hallucination_mode"][value="off"]');
                if (antiHallucinationRadio) {
                    antiHallucinationRadio.checked = true;
                }
                
                // Pre-configure selected model
                if ('') {
                    selectedModel = '';
                    localStorage.setItem('selectedModel', selectedModel);
                }
                
                // Category validation for custom interface
                if (["CANOPY", "RISE"].length > 0) {
                    const originalSendQuery = window.sendQuery;
                    window.sendQuery = function() {
                        const category = document.getElementById('category').value;
                        const allowedCategories = ["CANOPY", "RISE"];
                        
                        if (allowedCategories.length > 0 && !allowedCategories.includes(category)) {
                            alert('This interface is limited to the following categories: ' + allowedCategories.join(', '));
                            return;
                        }
                        
                        return originalSendQuery.apply(this, arguments);
                    };
                }
            });
        </script>
        
</body>
</html>