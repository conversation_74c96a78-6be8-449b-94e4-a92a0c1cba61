#!/usr/bin/env python3
"""
Diagnostic test for HTML Generator issues.
"""

import os
import sys
import traceback

def test_html_generator_import():
    """Test if HTMLGenerator can be imported."""
    print("=" * 50)
    print("HTML Generator Diagnostic Test")
    print("=" * 50)
    
    try:
        from html_generator import HTMLGenerator
        print("✓ HTMLGenerator imported successfully")
        return HTMLGenerator()
    except Exception as e:
        print(f"✗ HTMLGenerator import failed: {e}")
        traceback.print_exc()
        return None

def test_configuration_methods(generator):
    """Test configuration methods."""
    if not generator:
        return False
    
    print("\n1. Testing get_available_categories()...")
    try:
        categories = generator.get_available_categories()
        print(f"   ✓ Categories: {categories}")
    except Exception as e:
        print(f"   ✗ Categories error: {e}")
        traceback.print_exc()
        return False
    
    print("\n2. Testing get_anti_hallucination_modes()...")
    try:
        modes = generator.get_anti_hallucination_modes()
        available_modes = modes.get('available_modes', [])
        print(f"   ✓ Anti-hallucination modes: {available_modes}")
    except Exception as e:
        print(f"   ✗ Anti-hallucination modes error: {e}")
        traceback.print_exc()
        return False
    
    print("\n3. Testing get_available_models()...")
    try:
        models = generator.get_available_models()
        print(f"   ✓ Models: {len(models)} models found")
        if models:
            print(f"   First model: {models[0].get('display_name', models[0]['name'])}")
    except Exception as e:
        print(f"   ✗ Models error: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_flask_dependencies():
    """Test if Flask dependencies are available."""
    print("\n4. Testing Flask dependencies...")
    
    dependencies = [
        'flask',
        'flask_wtf',
        'flask_login',
        'jinja2',
        'werkzeug'
    ]
    
    missing = []
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"   ✓ {dep}")
        except ImportError:
            print(f"   ✗ {dep} - MISSING")
            missing.append(dep)
    
    if missing:
        print(f"\n   Missing dependencies: {', '.join(missing)}")
        return False
    return True

def test_file_structure():
    """Test if required files exist."""
    print("\n5. Testing file structure...")
    
    required_files = [
        'templates/index.html',
        'default_models.json',
        'chroma'
    ]
    
    missing = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✓ {file_path}")
        else:
            print(f"   ✗ {file_path} - MISSING")
            missing.append(file_path)
    
    if missing:
        print(f"\n   Missing files: {', '.join(missing)}")
        return False
    return True

def test_api_endpoint_simulation():
    """Simulate the API endpoint logic."""
    print("\n6. Testing API endpoint simulation...")
    
    try:
        from html_generator import HTMLGenerator
        generator = HTMLGenerator()
        
        # Simulate the /api/interface-config-options endpoint
        categories = generator.get_available_categories()
        models = generator.get_available_models()
        anti_hallucination_modes = generator.get_anti_hallucination_modes()
        
        result = {
            'success': True,
            'options': {
                'categories': categories,
                'models': [{'name': m['name'], 'display_name': m.get('display_name', m['name'])} for m in models],
                'anti_hallucination_modes': anti_hallucination_modes
            }
        }
        
        print(f"   ✓ API simulation successful")
        print(f"   Categories: {len(categories)}")
        print(f"   Models: {len(models)}")
        print(f"   Modes: {len(anti_hallucination_modes.get('available_modes', []))}")
        
        return result
        
    except Exception as e:
        print(f"   ✗ API simulation failed: {e}")
        traceback.print_exc()
        return None

def main():
    """Main diagnostic function."""
    
    # Test 1: File structure
    if not test_file_structure():
        print("\n❌ File structure test failed. Please ensure all required files exist.")
        return False
    
    # Test 2: Flask dependencies
    flask_ok = test_flask_dependencies()
    
    # Test 3: HTML Generator import
    generator = test_html_generator_import()
    
    # Test 4: Configuration methods
    config_ok = test_configuration_methods(generator)
    
    # Test 5: API simulation
    api_result = test_api_endpoint_simulation()
    
    print("\n" + "=" * 50)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    if not flask_ok:
        print("❌ FLASK DEPENDENCIES MISSING")
        print("   Solution: Install Flask dependencies")
        print("   Command: pip install Flask Flask-WTF Flask-Login")
        return False
    
    if not generator:
        print("❌ HTML GENERATOR IMPORT FAILED")
        print("   Solution: Check html_generator.py for syntax errors")
        return False
    
    if not config_ok:
        print("❌ CONFIGURATION METHODS FAILED")
        print("   Solution: Check file paths and dependencies")
        return False
    
    if not api_result:
        print("❌ API SIMULATION FAILED")
        print("   Solution: Check Flask app integration")
        return False
    
    print("✅ ALL TESTS PASSED")
    print("   The HTML Generator should work correctly")
    print("   Issue might be in Flask app startup or routing")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
