"""
Permission management module for the user management system.

This module provides functions for managing permissions, permission groups,
and related operations.
"""

import logging
from typing import Dict, List, Tuple, Optional, Any, Union
from functools import wraps
from flask import request, redirect, url_for, flash
from flask_login import current_user

import db
from user_models import PermissionGroup

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def function_permission_required(function_name: str):
    """
    Decorator to require a specific dashboard function permission.

    Args:
        function_name: The dashboard function to check permissions for

    Returns:
        A decorator function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('user.login'))

            if not current_user.has_dashboard_permission(function_name):
                flash('You do not have permission to access this page.', 'error')
                return redirect(url_for('admin_dashboard'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator


def admin_required(f):
    """
    Decorator to require admin authentication for routes.

    Args:
        f: The function to decorate

    Returns:
        The decorated function
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Skip authentication for the admin dashboard (which handles login)
        if request.endpoint == 'admin_dashboard':
            return f(*args, **kwargs)

        # For all other admin routes, require authentication
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('admin_dashboard'))

        return f(*args, **kwargs)
    return decorated_function


def get_permission_groups() -> List[PermissionGroup]:
    """
    Get all permission groups with their permissions.

    Returns:
        List of PermissionGroup objects
    """
    return PermissionGroup.get_all_groups()


def get_dashboard_functions() -> List[Dict[str, Any]]:
    """
    Get all available dashboard functions.

    Returns:
        List of dictionaries with function information
    """
    return [
        {
            'name': 'upload_content',
            'description': 'Upload new documents and content',
            'category': 'Content Management',
            'default_roles': ['admin', 'editor']
        },
        {
            'name': 'manage_files',
            'description': 'Manage existing files and documents',
            'category': 'Content Management',
            'default_roles': ['admin', 'editor']
        },
        {
            'name': 'model_settings',
            'description': 'Configure AI models and settings',
            'category': 'System Settings',
            'default_roles': ['admin']
        },
        {
            'name': 'chat_history',
            'description': 'View and manage chat history',
            'category': 'Chat Management',
            'default_roles': ['admin', 'editor', 'viewer']
        },
        {
            'name': 'chat_sessions',
            'description': 'View and manage chat sessions',
            'category': 'Chat Management',
            'default_roles': ['admin', 'editor', 'viewer']
        },
        {
            'name': 'ai_analytics',
            'description': 'View AI usage analytics',
            'category': 'Analytics',
            'default_roles': ['admin', 'editor', 'viewer']
        },
        {
            'name': 'model_performance_analysis',
            'description': 'View model performance analysis and research data',
            'category': 'Analytics',
            'default_roles': ['admin']
        },
        {
            'name': 'clean_urls',
            'description': 'Clean and manage URL content',
            'category': 'Content Management',
            'default_roles': ['admin', 'editor']
        },
        {
            'name': 'user_management',
            'description': 'Manage users and permissions',
            'category': 'System Settings',
            'default_roles': ['admin']
        },
        {
            'name': 'greeting_management',
            'description': 'Manage greeting templates and personalization',
            'category': 'System Settings',
            'default_roles': ['admin']
        },
        {
            'name': 'edit_own_profile',
            'description': 'Edit own user profile',
            'category': 'User Profile',
            'default_roles': ['admin', 'editor', 'viewer']
        },
        {
            'name': 'vector_data',
            'description': 'View and manage vector database content',
            'category': 'Analytics',
            'default_roles': ['admin', 'editor']
        },
        {
            'name': 'admin_interface_generation',
            'description': 'Generate custom HTML chat interfaces',
            'category': 'System Settings',
            'default_roles': ['admin']
        }
    ]


def ensure_default_permission_groups():
    """
    Ensure default permission groups (Admin, Editor, Viewer) exist with appropriate permissions.
    This function should be called during system initialization.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Define default groups with their permissions
        default_groups = {
            'Admin': {
                'description': 'Full administrative access to all system functions',
                'permissions': {func['name']: True for func in get_dashboard_functions()}
            },
            'Editor': {
                'description': 'Content management and analytics access',
                'permissions': {
                    func['name']: 'editor' in func.get('default_roles', [])
                    for func in get_dashboard_functions()
                }
            },
            'Viewer': {
                'description': 'Read-only access to analytics and chat history',
                'permissions': {
                    func['name']: 'viewer' in func.get('default_roles', [])
                    for func in get_dashboard_functions()
                }
            }
        }

        for group_name, group_config in default_groups.items():
            # Check if group exists
            existing_groups = db.execute_query(
                "SELECT group_id FROM permission_groups WHERE name = ?",
                (group_name,)
            )

            if not existing_groups:
                # Create the group
                group_id = db.execute_insert(
                    "INSERT INTO permission_groups (name, description) VALUES (?, ?)",
                    (group_name, group_config['description'])
                )
                logger.info(f"Created default permission group: {group_name}")
            else:
                group_id = existing_groups[0]['group_id']
                logger.info(f"Default permission group already exists: {group_name}")

            # Ensure all permissions are set for this group
            for function_name, enabled in group_config['permissions'].items():
                # Check if permission exists
                existing_permission = db.execute_query(
                    "SELECT id FROM group_permissions WHERE group_id = ? AND function_name = ?",
                    (group_id, function_name)
                )

                if not existing_permission:
                    # Insert new permission
                    db.execute_insert(
                        "INSERT INTO group_permissions (group_id, function_name, enabled) VALUES (?, ?, ?)",
                        (group_id, function_name, 1 if enabled else 0)
                    )
                    logger.info(f"Added permission {function_name} to group {group_name}: {enabled}")

        return True
    except Exception as e:
        logger.error(f"Failed to ensure default permission groups: {str(e)}")
        return False


def sync_new_module_permissions():
    """
    Synchronize permissions for new modules across all existing permission groups.
    This function should be called when new modules are added to the system.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get all dashboard functions
        all_functions = get_dashboard_functions()

        # Get all existing permission groups
        groups = db.execute_query("SELECT group_id, name FROM permission_groups")

        for group in groups:
            group_id = group['group_id']
            group_name = group['name']

            # Get existing permissions for this group
            existing_permissions = db.execute_query(
                "SELECT function_name FROM group_permissions WHERE group_id = ?",
                (group_id,)
            )
            existing_function_names = {perm['function_name'] for perm in existing_permissions}

            # Find new functions that don't have permissions set
            for function in all_functions:
                function_name = function['name']

                if function_name not in existing_function_names:
                    # Determine default permission based on group name and function defaults
                    default_enabled = False

                    if group_name == 'Admin':
                        default_enabled = True
                    elif group_name in ['Editor', 'Viewer']:
                        role_name = group_name.lower()
                        default_enabled = role_name in function.get('default_roles', [])

                    # Insert the new permission
                    db.execute_insert(
                        "INSERT INTO group_permissions (group_id, function_name, enabled) VALUES (?, ?, ?)",
                        (group_id, function_name, 1 if default_enabled else 0)
                    )

                    logger.info(f"Added new module permission: {function_name} to group {group_name} (enabled: {default_enabled})")

        return True
    except Exception as e:
        logger.error(f"Failed to sync new module permissions: {str(e)}")
        return False


def auto_assign_user_to_group(user_id: int, role: str) -> bool:
    """
    Automatically assign a user to the appropriate permission group based on their role.

    Args:
        user_id: The user ID
        role: The user's role ('admin', 'editor', 'viewer')

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Map roles to group names
        role_to_group = {
            'admin': 'Admin',
            'editor': 'Editor',
            'viewer': 'Viewer'
        }

        group_name = role_to_group.get(role.lower())
        if not group_name:
            logger.warning(f"No default group mapping for role: {role}")
            return False

        # Get the group ID
        group_result = db.execute_query(
            "SELECT group_id FROM permission_groups WHERE name = ?",
            (group_name,)
        )

        if not group_result:
            logger.error(f"Default permission group '{group_name}' not found")
            return False

        group_id = group_result[0]['group_id']

        # Update the user's group assignment
        db.execute_update(
            "UPDATE users SET group_id = ? WHERE user_id = ?",
            (group_id, user_id)
        )

        logger.info(f"Assigned user {user_id} to group '{group_name}' based on role '{role}'")
        return True

    except Exception as e:
        logger.error(f"Failed to auto-assign user to group: {str(e)}")
        return False


def get_missing_permissions_for_groups() -> Dict[str, List[str]]:
    """
    Get a report of missing permissions for all groups.

    Returns:
        Dict mapping group names to lists of missing function names
    """
    try:
        missing_permissions = {}

        # Get all dashboard functions
        all_functions = {func['name'] for func in get_dashboard_functions()}

        # Get all permission groups
        groups = db.execute_query("SELECT group_id, name FROM permission_groups")

        for group in groups:
            group_id = group['group_id']
            group_name = group['name']

            # Get existing permissions for this group
            existing_permissions = db.execute_query(
                "SELECT function_name FROM group_permissions WHERE group_id = ?",
                (group_id,)
            )
            existing_function_names = {perm['function_name'] for perm in existing_permissions}

            # Find missing functions
            missing_functions = all_functions - existing_function_names

            if missing_functions:
                missing_permissions[group_name] = list(missing_functions)

        return missing_permissions

    except Exception as e:
        logger.error(f"Failed to get missing permissions report: {str(e)}")
        return {}


def create_permission_group(name: str, description: str, admin_user_id: Optional[int] = None) -> Tuple[bool, Union[int, str]]:
    """
    Create a new permission group.

    Args:
        name: The name of the group
        description: The description of the group
        admin_user_id: The ID of the admin user creating the group

    Returns:
        A tuple of (success, result). If successful, result is the group ID.
        If unsuccessful, result is an error message.
    """
    try:
        # Check if group already exists
        existing = db.execute_query(
            "SELECT COUNT(*) as count FROM permission_groups WHERE name = ?",
            (name,)
        )

        if existing and existing[0]['count'] > 0:
            return False, "A group with this name already exists"

        # Insert new group
        group_id = db.execute_insert(
            "INSERT INTO permission_groups (name, description) VALUES (?, ?)",
            (name, description)
        )

        # Log the group creation
        log_permission_change(
            admin_user_id=admin_user_id,
            target_user_id=None,
            change_type="group_created",
            entity_changed="permission_group",
            old_value="None",
            new_value=name
        )

        return True, group_id
    except Exception as e:
        logger.error(f"Failed to create permission group: {str(e)}")
        return False, f"Database error: {str(e)}"


def update_permission_group(group_id: int, name: str, description: str, admin_user_id: Optional[int] = None) -> Tuple[bool, str]:
    """
    Update an existing permission group.

    Args:
        group_id: The ID of the group to update
        name: The new name of the group
        description: The new description of the group
        admin_user_id: The ID of the admin user updating the group

    Returns:
        A tuple of (success, message)
    """
    try:
        # Get current group data for logging
        current_group = db.execute_query(
            "SELECT name, description FROM permission_groups WHERE group_id = ?",
            (group_id,)
        )

        if not current_group:
            return False, f"Group with ID {group_id} not found"

        old_name = current_group[0]['name']
        old_description = current_group[0]['description']

        # Check if new name already exists for another group
        if name != old_name:
            existing = db.execute_query(
                "SELECT COUNT(*) as count FROM permission_groups WHERE name = ? AND group_id != ?",
                (name, group_id)
            )

            if existing and existing[0]['count'] > 0:
                return False, "Another group with this name already exists"

        # Update group
        db.execute_update(
            "UPDATE permission_groups SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE group_id = ?",
            (name, description, group_id)
        )

        # Log the group update
        if name != old_name:
            log_permission_change(
                admin_user_id=admin_user_id,
                target_user_id=None,
                change_type="group_renamed",
                entity_changed="permission_group",
                old_value=old_name,
                new_value=name
            )

        if description != old_description:
            log_permission_change(
                admin_user_id=admin_user_id,
                target_user_id=None,
                change_type="group_description_changed",
                entity_changed=f"permission_group:{name}",
                old_value=old_description,
                new_value=description
            )

        return True, "Group updated successfully"
    except Exception as e:
        logger.error(f"Failed to update permission group: {str(e)}")
        return False, f"Database error: {str(e)}"


def update_group_permission(group_id: int, function_name: str, enabled: bool, admin_user_id: Optional[int] = None) -> Tuple[bool, str]:
    """
    Update a permission for a group.

    Args:
        group_id: The ID of the group
        function_name: The function name to update
        enabled: Whether the function is enabled
        admin_user_id: The ID of the admin user making the change

    Returns:
        A tuple of (success, message)
    """
    try:
        # Verify the group exists
        group_data = db.execute_query(
            "SELECT name FROM permission_groups WHERE group_id = ?",
            (group_id,)
        )

        if not group_data:
            return False, f"Group with ID {group_id} not found"

        group_name = group_data[0]['name']

        # Get current permission status for logging
        current_permission = db.execute_query(
            "SELECT enabled FROM group_permissions WHERE group_id = ? AND function_name = ?",
            (group_id, function_name)
        )

        old_value = "None" if not current_permission else "enabled" if current_permission[0]['enabled'] else "disabled"

        # Check if permission exists
        if current_permission:
            # Update existing permission
            db.execute_update(
                "UPDATE group_permissions SET enabled = ?, updated_at = CURRENT_TIMESTAMP WHERE group_id = ? AND function_name = ?",
                (1 if enabled else 0, group_id, function_name)
            )
        else:
            # Insert new permission
            db.execute_insert(
                "INSERT INTO group_permissions (group_id, function_name, enabled) VALUES (?, ?, ?)",
                (group_id, function_name, 1 if enabled else 0)
            )

        # Log the permission change
        log_permission_change(
            admin_user_id=admin_user_id,
            target_user_id=None,
            change_type="group_permission_change",
            entity_changed=f"{group_name}:{function_name}",
            old_value=old_value,
            new_value="enabled" if enabled else "disabled"
        )

        return True, "Permission updated successfully"
    except Exception as e:
        logger.error(f"Failed to update group permission: {str(e)}")
        return False, f"Database error: {str(e)}"


def log_permission_change(admin_user_id: Optional[int], target_user_id: Optional[int],
                         change_type: str, entity_changed: str, old_value: str,
                         new_value: str) -> bool:
    """
    Log a permission change to the audit log.

    Args:
        admin_user_id: The ID of the admin user making the change
        target_user_id: The ID of the user being affected
        change_type: The type of change
        entity_changed: The entity being changed
        old_value: The old value
        new_value: The new value

    Returns:
        True if successful, False otherwise
    """
    try:
        db.execute_insert("""
            INSERT INTO permission_audit_logs (
                admin_user_id, target_user_id, change_type, entity_changed,
                old_value, new_value, ip_address
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            admin_user_id,
            target_user_id,
            change_type,
            entity_changed,
            old_value,
            new_value,
            request.remote_addr if request else None
        ))

        return True
    except Exception as e:
        logger.error(f"Failed to log permission change: {str(e)}")
        return False
