import sqlite3
import os

print("=== Direct Database Check ===")

# Check if database exists
if os.path.exists('user_management.db'):
    print("✅ user_management.db found")
    
    try:
        conn = sqlite3.connect('user_management.db')
        cursor = conn.cursor()
        
        # List all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"\nTables found ({len(tables)}):")
        for table in tables:
            print(f"   - {table[0]}")
        
        # Check if permission tables exist
        permission_tables = ['permission_groups', 'group_permissions']
        existing_permission_tables = [t[0] for t in tables if t[0] in permission_tables]
        
        if len(existing_permission_tables) == 2:
            print("\n✅ Permission tables exist")
            
            # Check permission groups
            cursor.execute("SELECT group_id, name FROM permission_groups")
            groups = cursor.fetchall()
            print(f"\nPermission groups ({len(groups)}):")
            for group in groups:
                print(f"   - {group[1]} (ID: {group[0]})")
            
            # Check for admin_interface_generation
            cursor.execute("""
                SELECT pg.name, gp.function_name, gp.enabled 
                FROM permission_groups pg 
                JOIN group_permissions gp ON pg.group_id = gp.group_id 
                WHERE gp.function_name = 'admin_interface_generation'
            """)
            admin_perms = cursor.fetchall()
            
            if admin_perms:
                print(f"\n✅ admin_interface_generation permissions found ({len(admin_perms)}):")
                for perm in admin_perms:
                    status = "Enabled" if perm[2] else "Disabled"
                    print(f"   - {perm[0]}: {status}")
            else:
                print("\n❌ admin_interface_generation permissions NOT found")
                
                # Show all functions
                cursor.execute("SELECT DISTINCT function_name FROM group_permissions")
                functions = cursor.fetchall()
                print(f"\nAll functions in database ({len(functions)}):")
                for func in functions:
                    print(f"   - {func[0]}")
        else:
            print(f"\n❌ Permission tables missing. Found: {existing_permission_tables}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")
else:
    print("❌ user_management.db not found")

print("\n=== Check Complete ===")
