#!/usr/bin/env python3
"""
Demonstration script for the HTML Generator System.

This script demonstrates the key features of the HTML generation system
by creating several example interfaces with different configurations.
"""

import os
import sys
from html_generator import HTMLGenerator

def demo_interface_generation():
    """Demonstrate interface generation with various configurations."""
    print("🚀 HTML Generator System Demonstration")
    print("=" * 50)
    
    # Initialize generator
    generator = HTMLGenerator()
    
    # Demo configurations
    demo_configs = [
        {
            'name': 'CANOPY Research Portal',
            'config': {
                'interface_name': 'CANOPY Research Portal',
                'custom_title': 'ERDB CANOPY Research Interface',
                'anti_hallucination_mode': 'strict',
                'categories': ['CANOPY']
            },
            'description': 'Strict mode interface limited to CANOPY forestry research'
        },
        {
            'name': 'Technical Manual Assistant',
            'config': {
                'interface_name': 'Technical Manual Assistant',
                'anti_hallucination_mode': 'balanced',
                'selected_model': 'llama3.2:3b-instruct-q4_K_M',
                'categories': ['MANUAL']
            },
            'description': 'Balanced mode with pre-selected model for technical manuals'
        },
        {
            'name': 'Multi-Category Research Hub',
            'config': {
                'interface_name': 'Multi-Category Research Hub',
                'custom_title': 'ERDB Comprehensive Research Portal',
                'anti_hallucination_mode': 'off',
                'categories': ['CANOPY', 'RISE']
            },
            'description': 'Creative mode interface for multiple research categories'
        },
        {
            'name': 'General Knowledge Interface',
            'config': {
                'interface_name': 'General Knowledge Interface',
                'custom_title': 'ERDB Knowledge Hub - General Access'
            },
            'description': 'Flexible interface with user-configurable settings'
        }
    ]
    
    generated_interfaces = []
    
    print(f"\n📋 Generating {len(demo_configs)} demonstration interfaces...\n")
    
    for i, demo in enumerate(demo_configs, 1):
        print(f"{i}. {demo['name']}")
        print(f"   Description: {demo['description']}")
        
        try:
            result = generator.generate_interface(demo['config'])
            
            if result['success']:
                print(f"   ✅ Generated: {result['filename']}")
                print(f"   📁 Size: {result['metadata']['file_size']:,} bytes")
                generated_interfaces.append(result)
                
                # Show configuration summary
                config = result['metadata']['config']
                if config.get('anti_hallucination_mode'):
                    print(f"   🛡️  Mode: {config['anti_hallucination_mode']}")
                if config.get('selected_model'):
                    print(f"   🤖 Model: {config['selected_model']}")
                if config.get('categories'):
                    print(f"   📚 Categories: {', '.join(config['categories'])}")
                    
            else:
                print(f"   ❌ Failed: {result['error']}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            
        print()
    
    return generated_interfaces

def demo_interface_listing():
    """Demonstrate interface listing functionality."""
    print("📋 Listing Generated Interfaces")
    print("-" * 30)
    
    generator = HTMLGenerator()
    
    try:
        interfaces = generator.list_generated_interfaces()
        
        if not interfaces:
            print("No interfaces found.")
            return
            
        print(f"Found {len(interfaces)} generated interfaces:\n")
        
        for i, interface in enumerate(interfaces, 1):
            config = interface.get('config', {})
            print(f"{i}. {config.get('interface_name', 'Unnamed Interface')}")
            print(f"   📄 File: {interface['filename']}")
            print(f"   📅 Generated: {interface['generated_at']}")
            print(f"   📁 Size: {interface['file_size']:,} bytes")
            
            # Show configuration details
            if config.get('anti_hallucination_mode'):
                print(f"   🛡️  Mode: {config['anti_hallucination_mode']}")
            if config.get('selected_model'):
                print(f"   🤖 Model: {config['selected_model']}")
            if config.get('categories'):
                print(f"   📚 Categories: {', '.join(config['categories'])}")
            print()
            
    except Exception as e:
        print(f"Error listing interfaces: {str(e)}")

def demo_configuration_options():
    """Demonstrate available configuration options."""
    print("⚙️  Available Configuration Options")
    print("-" * 35)
    
    generator = HTMLGenerator()
    
    try:
        # Show categories
        categories = generator.get_available_categories()
        print(f"📚 Document Categories ({len(categories)}):")
        for category in categories:
            print(f"   • {category}")
        print()
        
        # Show models
        models = generator.get_available_models()
        print(f"🤖 Available Models ({len(models)}):")
        for model in models[:5]:  # Show first 5
            print(f"   • {model.get('display_name', model['name'])}")
            if model.get('description'):
                print(f"     {model['description']}")
        if len(models) > 5:
            print(f"   ... and {len(models) - 5} more models")
        print()
        
        # Show anti-hallucination modes
        modes = generator.get_anti_hallucination_modes()
        print("🛡️  Anti-Hallucination Modes:")
        available_modes = modes.get('available_modes', [])
        descriptions = modes.get('mode_descriptions', {})
        
        for mode in available_modes:
            desc = descriptions.get(mode, mode.title())
            print(f"   • {mode.title()}: {desc}")
        print()
        
    except Exception as e:
        print(f"Error getting configuration options: {str(e)}")

def demo_cleanup(generated_interfaces):
    """Demonstrate cleanup functionality."""
    if not generated_interfaces:
        return
        
    print("🧹 Cleanup Demonstration")
    print("-" * 25)
    
    generator = HTMLGenerator()
    
    # Ask user if they want to clean up demo files
    response = input(f"Delete {len(generated_interfaces)} demo interfaces? (y/N): ").strip().lower()
    
    if response == 'y':
        print("\nCleaning up demo interfaces...")
        
        for interface in generated_interfaces:
            filename = interface['filename']
            try:
                result = generator.delete_interface(filename)
                if result['success']:
                    print(f"   ✅ Deleted: {filename}")
                else:
                    print(f"   ❌ Failed to delete {filename}: {result['error']}")
            except Exception as e:
                print(f"   ❌ Error deleting {filename}: {str(e)}")
    else:
        print("\nDemo interfaces preserved. You can view them at:")
        for interface in generated_interfaces:
            print(f"   • /generated/{interface['filename']}")

def main():
    """Main demonstration function."""
    print("HTML Generator System - Live Demonstration")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("templates/index.html"):
        print("❌ Error: templates/index.html not found.")
        print("Please run this script from the project root directory.")
        sys.exit(1)
    
    try:
        # Show available configuration options
        demo_configuration_options()
        
        # Generate demonstration interfaces
        generated_interfaces = demo_interface_generation()
        
        # List all generated interfaces
        demo_interface_listing()
        
        # Show access information
        if generated_interfaces:
            print("🌐 Access Generated Interfaces")
            print("-" * 30)
            print("Start the Flask application and visit:")
            for interface in generated_interfaces:
                config = interface['metadata']['config']
                name = config.get('interface_name', 'Unnamed')
                print(f"   • {name}: http://localhost:5000/generated/{interface['filename']}")
            print()
        
        # Cleanup demonstration
        demo_cleanup(generated_interfaces)
        
        print("\n🎉 Demonstration completed successfully!")
        print("\nNext steps:")
        print("1. Start the Flask application: python app.py")
        print("2. Visit the admin dashboard: http://localhost:5000/admin/dashboard")
        print("3. Click on 'Interface Generator' to create custom interfaces")
        print("4. Access generated interfaces via /generated/<filename>")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demonstration interrupted by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
