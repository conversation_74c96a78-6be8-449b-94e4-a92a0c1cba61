#!/usr/bin/env python3
"""
Install minimal dependencies for HTML Generator functionality.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install {package}: {e}")
        return False

def main():
    """Install essential packages for HTML Generator."""
    print("Installing HTML Generator Dependencies")
    print("=" * 40)
    
    # Essential packages for HTML Generator
    packages = [
        "Flask==3.1.0",
        "Flask-WTF==1.2.2", 
        "Flask-Login==0.6.3",
        "Werkzeug==3.1.3",
        "Jinja2==3.1.6",
        "WTForms==3.2.1",
        "python-dotenv==1.1.0"
    ]
    
    failed = []
    
    for package in packages:
        print(f"Installing {package}...")
        if install_package(package):
            print(f"✓ {package} installed successfully")
        else:
            print(f"✗ {package} installation failed")
            failed.append(package)
    
    print("\n" + "=" * 40)
    
    if failed:
        print(f"❌ Failed to install: {', '.join(failed)}")
        print("Please install manually:")
        for pkg in failed:
            print(f"   pip install {pkg}")
        return False
    else:
        print("✅ All dependencies installed successfully!")
        print("\nNext steps:")
        print("1. Run: python app.py")
        print("2. Open: http://localhost:5000/admin/dashboard")
        print("3. Click: Interface Generator")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
